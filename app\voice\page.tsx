"use client";

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Mic, MicOff, Volume2, VolumeX, Play, Pause, Brain, AudioWaveform as Waveform, MessageCircle } from 'lucide-react';
import { geminiClient, type ChatMessage as GeminiChatMessage } from '@/lib/gemini';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

interface VoiceSession {
  id: string;
  timestamp: Date;
  transcript: string;
  response: string;
  duration: number;
}

export default function VoicePage() {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [audioLevel, setAudioLevel] = useState(0);
  const [sessions, setSessions] = useState<VoiceSession[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const recognition = useRef<SpeechRecognition | null>(null);
  const synthesis = useRef<SpeechSynthesis | null>(null);
  const audioContext = useRef<AudioContext | null>(null);
  const analyser = useRef<AnalyserNode | null>(null);
  const microphone = useRef<MediaStreamAudioSourceNode | null>(null);

  useEffect(() => {
    // Initialize Web Speech API
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = true;
      recognition.current.interimResults = true;
      recognition.current.lang = 'en-US';

      recognition.current.onresult = (event) => {
        let transcript = '';
        for (let i = event.resultIndex; i < event.results.length; i++) {
          transcript += event.results[i][0].transcript;
        }
        setCurrentTranscript(transcript);
      };

      recognition.current.onend = () => {
        setIsListening(false);
        if (currentTranscript.trim()) {
          processVoiceInput(currentTranscript);
        }
      };
    }

    // Initialize Speech Synthesis
    if ('speechSynthesis' in window) {
      synthesis.current = window.speechSynthesis;
    }

    // Initialize Audio Context for visualization
    initializeAudioVisualization();

    return () => {
      if (audioContext.current) {
        audioContext.current.close();
      }
    };
  }, []);

  const initializeAudioVisualization = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioContext.current = new AudioContext();
      analyser.current = audioContext.current.createAnalyser();
      microphone.current = audioContext.current.createMediaStreamSource(stream);
      
      analyser.current.fftSize = 256;
      microphone.current.connect(analyser.current);
      
      visualizeAudio();
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  const visualizeAudio = () => {
    if (!analyser.current) return;

    const bufferLength = analyser.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const updateLevel = () => {
      analyser.current!.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((a, b) => a + b) / bufferLength;
      setAudioLevel(Math.min(average / 128 * 100, 100));
      
      if (isListening) {
        requestAnimationFrame(updateLevel);
      }
    };

    if (isListening) {
      updateLevel();
    }
  };

  const toggleListening = () => {
    if (!recognition.current) return;

    if (isListening) {
      recognition.current.stop();
      setIsListening(false);
      setAudioLevel(0);
    } else {
      setCurrentTranscript('');
      recognition.current.start();
      setIsListening(true);
      visualizeAudio();
    }
  };

  const processVoiceInput = async (transcript: string) => {
    setIsProcessing(true);

    try {
      // Check if the query is finance-related
      if (!isFinanceRelated(transcript)) {
        const warningResponse = "I'm your AI Financial Strategist, and I specialize in financial topics. Please ask me about budgeting, investing, retirement planning, or other financial matters. How can I help you with your financial goals today?";

        const session: VoiceSession = {
          id: Date.now().toString(),
          timestamp: new Date(),
          transcript,
          response: warningResponse,
          duration: 0
        };

        setSessions(prev => [session, ...prev]);
        speakResponse(warningResponse);
        setCurrentTranscript('');
        setIsProcessing(false);
        return;
      }

      // Call Gemini API for financial queries
      const chatHistory: GeminiChatMessage[] = [
        {
          role: 'user',
          content: transcript
        }
      ];

      const response = await geminiClient.generateContent(chatHistory, {
        userBehavior: { focusArea: 'financial_planning' },
        financialProfile: { experienceLevel: 'mixed' }
      });

      // Convert markdown response to speech-friendly text
      const speechText = convertMarkdownToSpeech(response.content);

      const session: VoiceSession = {
        id: Date.now().toString(),
        timestamp: new Date(),
        transcript,
        response: response.content, // Keep full markdown for display
        duration: 0
      };

      setSessions(prev => [session, ...prev]);
      speakResponse(speechText); // Speak the cleaned version
      setCurrentTranscript('');
      setIsProcessing(false);
    } catch (error) {
      console.error('Error processing voice input:', error);
      const errorResponse = "I apologize, but I'm experiencing technical difficulties. Please try again in a moment. I'm here to help with your financial questions.";

      const session: VoiceSession = {
        id: Date.now().toString(),
        timestamp: new Date(),
        transcript,
        response: errorResponse,
        duration: 0
      };

      setSessions(prev => [session, ...prev]);
      speakResponse(errorResponse);
      setCurrentTranscript('');
      setIsProcessing(false);
    }
  };

  // Check if the user's query is finance-related
  const isFinanceRelated = (input: string): boolean => {
    const financeKeywords = [
      // Core financial terms
      'budget', 'money', 'finance', 'financial', 'investment', 'invest', 'portfolio', 'stock', 'bond',
      'retirement', 'pension', 'savings', 'save', 'debt', 'loan', 'credit', 'mortgage', 'insurance',
      'tax', 'taxes', 'income', 'salary', 'expense', 'spending', 'cost', 'price', 'value', 'worth',

      // Investment terms
      'etf', 'mutual fund', 'index fund', 'dividend', 'yield', 'return', 'roi', 'risk', 'diversification',
      'asset', 'allocation', 'rebalance', 'compound', 'interest', 'inflation', 'market', 'trading',

      // Banking and accounts
      'bank', 'account', 'checking', 'savings', 'cd', 'certificate', 'deposit', 'withdrawal', 'transfer',
      'ira', '401k', 'roth', 'traditional', 'hsa', 'emergency fund',

      // Financial planning
      'goal', 'planning', 'strategy', 'advice', 'guidance', 'recommendation', 'analysis', 'calculation',
      'net worth', 'cash flow', 'liquidity', 'solvency', 'bankruptcy', 'foreclosure'
    ];

    const lowerInput = input.toLowerCase();
    return financeKeywords.some(keyword => lowerInput.includes(keyword)) ||
           // Also check for financial question patterns
           /how much|how to|what is|explain|calculate|compare|should i|can i|help me/i.test(input);
  };

  // Convert markdown response to speech-friendly text
  const convertMarkdownToSpeech = (markdown: string): string => {
    let speechText = markdown;

    // Remove markdown headers and replace with spoken equivalents
    speechText = speechText.replace(/^#{1,6}\s*(.+)$/gm, '$1. ');

    // Remove bold and italic formatting
    speechText = speechText.replace(/\*\*(.*?)\*\*/g, '$1');
    speechText = speechText.replace(/\*(.*?)\*/g, '$1');

    // Remove emojis and special characters
    speechText = speechText.replace(/[📋🎯💡🌍📈✅🔍📊]/g, '');

    // Replace bullet points with spoken format
    speechText = speechText.replace(/^[-•]\s*/gm, 'Point: ');
    speechText = speechText.replace(/^\d+\.\s*/gm, 'Step $&');

    // Clean up extra whitespace and line breaks
    speechText = speechText.replace(/\n\s*\n/g, '. ');
    speechText = speechText.replace(/\n/g, ' ');
    speechText = speechText.replace(/\s+/g, ' ');

    // Limit length for speech (keep first 500 characters for better voice experience)
    if (speechText.length > 500) {
      speechText = speechText.substring(0, 500) + '... For more details, please check the full response on screen.';
    }

    return speechText.trim();
  };

  const speakResponse = (text: string) => {
    if (!synthesis.current) return;

    setIsSpeaking(true);
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;
    
    utterance.onend = () => {
      setIsSpeaking(false);
    };

    synthesis.current.speak(utterance);
  };

  const stopSpeaking = () => {
    if (synthesis.current) {
      synthesis.current.cancel();
      setIsSpeaking(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold text-white">Voice Intelligence</h1>
            <p className="text-slate-300 max-w-2xl mx-auto">
              Speak naturally with your AI Financial Strategist. Get instant voice responses 
              and hands-free financial guidance powered by advanced speech recognition.
            </p>
          </div>

          {/* Main Voice Interface */}
          <div className="max-w-4xl mx-auto">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardHeader className="text-center">
                <CardTitle className="text-white flex items-center justify-center gap-2">
                  <Brain className="h-5 w-5 text-purple-400" />
                  AI Voice Assistant
                </CardTitle>
                <CardDescription className="text-slate-400">
                  {isListening ? 'Listening...' : isProcessing ? 'Processing...' : isSpeaking ? 'Speaking...' : 'Ready to help'}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-8">
                
                {/* Voice Visualization */}
                <div className="flex justify-center">
                  <div className="relative">
                    <Button
                      size="lg"
                      onClick={toggleListening}
                      disabled={isProcessing || isSpeaking}
                      className={`w-32 h-32 rounded-full ${
                        isListening 
                          ? 'bg-red-600 hover:bg-red-700 animate-pulse' 
                          : 'bg-purple-600 hover:bg-purple-700'
                      } text-white shadow-lg transition-all duration-300`}
                    >
                      {isListening ? (
                        <MicOff className="h-12 w-12" />
                      ) : (
                        <Mic className="h-12 w-12" />
                      )}
                    </Button>
                    
                    {/* Audio Level Visualization */}
                    {isListening && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-40 h-40 rounded-full border-4 border-purple-400/30">
                          <div className="w-full h-full rounded-full flex items-center justify-center">
                            <div 
                              className="bg-purple-400/20 rounded-full transition-all duration-100"
                              style={{
                                width: `${80 + audioLevel * 0.6}px`,
                                height: `${80 + audioLevel * 0.6}px`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Audio Level Meter */}
                {isListening && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <Waveform className="h-4 w-4 text-purple-400" />
                      <span className="text-sm text-slate-300">Audio Level</span>
                    </div>
                    <Progress value={audioLevel} className="h-2" />
                  </div>
                )}

                {/* Current Transcript */}
                {currentTranscript && (
                  <Card className="bg-slate-700/50 border-slate-600">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-2">
                        <MessageCircle className="h-4 w-4 text-blue-400 mt-1" />
                        <div>
                          <p className="text-sm font-medium text-slate-300 mb-1">You said:</p>
                          <p className="text-white">{currentTranscript}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Controls */}
                <div className="flex justify-center gap-4">
                  <Button
                    variant="outline"
                    onClick={toggleListening}
                    disabled={isProcessing || isSpeaking}
                    className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  >
                    {isListening ? (
                      <>
                        <MicOff className="h-4 w-4 mr-2" />
                        Stop Listening
                      </>
                    ) : (
                      <>
                        <Mic className="h-4 w-4 mr-2" />
                        Start Listening
                      </>
                    )}
                  </Button>
                  
                  {isSpeaking && (
                    <Button
                      variant="outline"
                      onClick={stopSpeaking}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <VolumeX className="h-4 w-4 mr-2" />
                      Stop Speaking
                    </Button>
                  )}
                </div>

                {/* Status Badges */}
                <div className="flex justify-center gap-2">
                  <Badge 
                    variant="outline" 
                    className={`${
                      isListening 
                        ? 'bg-red-500/10 text-red-300 border-red-500/30' 
                        : 'bg-slate-500/10 text-slate-300 border-slate-500/30'
                    }`}
                  >
                    {isListening ? 'Recording' : 'Standby'}
                  </Badge>
                  
                  <Badge 
                    variant="outline" 
                    className={`${
                      isSpeaking 
                        ? 'bg-blue-500/10 text-blue-300 border-blue-500/30' 
                        : 'bg-slate-500/10 text-slate-300 border-slate-500/30'
                    }`}
                  >
                    {isSpeaking ? 'Speaking' : 'Silent'}
                  </Badge>
                  
                  <Badge 
                    variant="outline" 
                    className={`${
                      isProcessing 
                        ? 'bg-yellow-500/10 text-yellow-300 border-yellow-500/30' 
                        : 'bg-green-500/10 text-green-300 border-green-500/30'
                    }`}
                  >
                    {isProcessing ? 'Processing' : 'Ready'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Sessions */}
          {sessions.length > 0 && (
            <div className="max-w-4xl mx-auto">
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">Recent Voice Sessions</CardTitle>
                  <CardDescription className="text-slate-400">
                    Your conversation history with the AI assistant
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {sessions.map((session) => (
                      <div key={session.id} className="space-y-3 p-4 rounded-lg bg-slate-700/30 border border-slate-600">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-slate-400">
                            {session.timestamp.toLocaleTimeString()}
                          </span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => speakResponse(session.response)}
                            className="text-slate-400 hover:text-white"
                          >
                            <Volume2 className="h-3 w-3" />
                          </Button>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-start gap-2">
                            <MessageCircle className="h-4 w-4 text-blue-400 mt-1" />
                            <div>
                              <p className="text-xs font-medium text-slate-400">You:</p>
                              <p className="text-sm text-slate-200">{session.transcript}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-start gap-2">
                            <Brain className="h-4 w-4 text-purple-400 mt-1" />
                            <div className="flex-1">
                              <p className="text-xs font-medium text-slate-400 mb-2">AI:</p>
                              <div className="bg-slate-600/30 rounded-lg p-3">
                                <MarkdownRenderer
                                  content={session.response}
                                  className="text-xs"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}