// Advanced Memory System with Reinforcement Learning Principles
import { supabase } from './supabase-client';

export interface UserVector {
  userId: string;
  conceptMastery: Record<string, number>; // 0-1 mastery scores
  riskProfile: {
    tolerance: number; // 1-10 scale
    capacity: number; // financial capacity
    timeHorizon: number; // years
    volatilityComfort: number; // 0-1 scale
  };
  learningStyle: {
    preferredComplexity: 'beginner' | 'intermediate' | 'advanced';
    interactionFrequency: number; // sessions per week
    topicPreferences: string[];
    responseLength: 'concise' | 'detailed' | 'comprehensive';
  };
  behavioralPatterns: {
    sessionDuration: number; // average minutes
    questionTypes: Record<string, number>; // frequency of question categories
    engagementScore: number; // 0-1 based on interactions
    lastActiveTopics: string[];
  };
  conceptConnections: Record<string, string[]>; // learned concept relationships
  progressHistory: Array<{
    timestamp: Date;
    concept: string;
    masteryGain: number;
    sessionContext: string;
  }>;
}

export interface ConceptNode {
  id: string;
  name: string;
  description: string;
  difficulty: number; // 1-10
  prerequisites: string[];
  relatedConcepts: string[];
  masteryThreshold: number; // 0-1
  examples: string[];
  practicalApplications: string[];
  category: 'investing' | 'risk' | 'planning' | 'analysis' | 'behavioral';
}

export class MemorySystem {
  private userVectors: Map<string, UserVector> = new Map();
  private conceptGraph: Map<string, ConceptNode> = new Map();
  private interactionHistory: Map<string, Array<any>> = new Map();

  constructor() {
    this.initializeConceptGraph();
  }

  private initializeConceptGraph() {
    const concepts: ConceptNode[] = [
      {
        id: 'asset_allocation',
        name: 'Asset Allocation',
        description: 'Strategic distribution of investments across different asset classes',
        difficulty: 3,
        prerequisites: ['diversification', 'risk_return'],
        relatedConcepts: ['portfolio_theory', 'rebalancing', 'strategic_allocation'],
        masteryThreshold: 0.7,
        examples: ['60/40 stocks/bonds', 'Target-date funds', 'Age-based allocation'],
        practicalApplications: ['Portfolio construction', 'Risk management', 'Retirement planning'],
        category: 'investing'
      },
      {
        id: 'sharpe_ratio',
        name: 'Sharpe Ratio',
        description: 'Risk-adjusted return measure comparing excess return to volatility',
        difficulty: 6,
        prerequisites: ['risk_return', 'standard_deviation', 'risk_free_rate'],
        relatedConcepts: ['sortino_ratio', 'information_ratio', 'risk_metrics'],
        masteryThreshold: 0.8,
        examples: ['Portfolio comparison', 'Fund evaluation', 'Strategy assessment'],
        practicalApplications: ['Investment selection', 'Performance evaluation', 'Risk assessment'],
        category: 'analysis'
      },
      {
        id: 'var',
        name: 'Value at Risk (VaR)',
        description: 'Statistical measure of potential portfolio losses over a specific time period',
        difficulty: 8,
        prerequisites: ['probability', 'normal_distribution', 'portfolio_theory'],
        relatedConcepts: ['conditional_var', 'expected_shortfall', 'stress_testing'],
        masteryThreshold: 0.85,
        examples: ['1% daily VaR', 'Portfolio risk limits', 'Regulatory capital'],
        practicalApplications: ['Risk management', 'Position sizing', 'Regulatory compliance'],
        category: 'risk'
      },
      {
        id: 'convexity',
        name: 'Bond Convexity',
        description: 'Measure of bond price sensitivity to interest rate changes beyond duration',
        difficulty: 9,
        prerequisites: ['duration', 'yield_curve', 'bond_pricing'],
        relatedConcepts: ['duration', 'yield_sensitivity', 'immunization'],
        masteryThreshold: 0.9,
        examples: ['Mortgage-backed securities', 'Callable bonds', 'Portfolio immunization'],
        practicalApplications: ['Bond portfolio management', 'Interest rate hedging', 'Fixed income analysis'],
        category: 'analysis'
      },
      {
        id: 'diversification',
        name: 'Diversification',
        description: 'Risk reduction through spreading investments across different assets',
        difficulty: 2,
        prerequisites: ['correlation', 'risk_return'],
        relatedConcepts: ['asset_allocation', 'correlation', 'systematic_risk'],
        masteryThreshold: 0.6,
        examples: ['Geographic diversification', 'Sector diversification', 'Asset class diversification'],
        practicalApplications: ['Portfolio construction', 'Risk reduction', 'Return optimization'],
        category: 'investing'
      }
    ];

    concepts.forEach(concept => {
      this.conceptGraph.set(concept.id, concept);
    });
  }

  async getUserVector(userId: string): Promise<UserVector> {
    if (this.userVectors.has(userId)) {
      return this.userVectors.get(userId)!;
    }

    // Try to load from Supabase
    const { data, error } = await supabase
      .from('user_vectors')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (data && !error) {
      const userVector = this.deserializeUserVector(data);
      this.userVectors.set(userId, userVector);
      return userVector;
    }

    // Create new user vector
    const newVector: UserVector = {
      userId,
      conceptMastery: {},
      riskProfile: {
        tolerance: 5,
        capacity: 5,
        timeHorizon: 10,
        volatilityComfort: 0.5
      },
      learningStyle: {
        preferredComplexity: 'beginner',
        interactionFrequency: 3,
        topicPreferences: [],
        responseLength: 'detailed'
      },
      behavioralPatterns: {
        sessionDuration: 15,
        questionTypes: {},
        engagementScore: 0.5,
        lastActiveTopics: []
      },
      conceptConnections: {},
      progressHistory: []
    };

    this.userVectors.set(userId, newVector);
    await this.saveUserVector(userId, newVector);
    return newVector;
  }

  async updateUserVector(userId: string, updates: Partial<UserVector>): Promise<void> {
    const currentVector = await this.getUserVector(userId);
    const updatedVector = { ...currentVector, ...updates };
    this.userVectors.set(userId, updatedVector);
    await this.saveUserVector(userId, updatedVector);
  }

  async recordInteraction(userId: string, interaction: {
    type: 'question' | 'concept_exploration' | 'simulation' | 'voice_session';
    content: string;
    concepts: string[];
    duration: number;
    engagement: number; // 0-1
    complexity: number; // 1-10
  }): Promise<void> {
    const userVector = await this.getUserVector(userId);
    
    // Update behavioral patterns
    userVector.behavioralPatterns.sessionDuration = 
      (userVector.behavioralPatterns.sessionDuration * 0.9) + (interaction.duration * 0.1);
    
    userVector.behavioralPatterns.engagementScore = 
      (userVector.behavioralPatterns.engagementScore * 0.8) + (interaction.engagement * 0.2);

    // Update concept mastery using reinforcement learning principles
    for (const conceptId of interaction.concepts) {
      const currentMastery = userVector.conceptMastery[conceptId] || 0;
      const masteryGain = this.calculateMasteryGain(interaction, conceptId);
      userVector.conceptMastery[conceptId] = Math.min(1, currentMastery + masteryGain);
      
      // Record progress
      userVector.progressHistory.push({
        timestamp: new Date(),
        concept: conceptId,
        masteryGain,
        sessionContext: interaction.type
      });
    }

    // Update learning style preferences
    if (interaction.engagement > 0.7) {
      if (interaction.complexity > 7) {
        userVector.learningStyle.preferredComplexity = 'advanced';
      } else if (interaction.complexity > 4) {
        userVector.learningStyle.preferredComplexity = 'intermediate';
      }
    }

    await this.updateUserVector(userId, userVector);
  }

  private calculateMasteryGain(interaction: any, conceptId: string): number {
    const concept = this.conceptGraph.get(conceptId);
    if (!concept) return 0;

    // Base gain from engagement and complexity match
    let gain = interaction.engagement * 0.1;
    
    // Bonus for appropriate complexity
    const complexityMatch = 1 - Math.abs(interaction.complexity - concept.difficulty) / 10;
    gain *= (0.5 + complexityMatch * 0.5);
    
    // Diminishing returns as mastery increases
    const currentMastery = interaction.currentMastery || 0;
    gain *= (1 - currentMastery);
    
    return gain;
  }

  getRecommendedConcepts(userId: string): string[] {
    const userVector = this.userVectors.get(userId);
    if (!userVector) return [];

    const recommendations: Array<{ conceptId: string; score: number }> = [];

    for (const [conceptId, concept] of this.conceptGraph) {
      const currentMastery = userVector.conceptMastery[conceptId] || 0;
      
      // Skip if already mastered
      if (currentMastery >= concept.masteryThreshold) continue;
      
      // Check prerequisites
      const prerequisitesMet = concept.prerequisites.every(
        prereq => (userVector.conceptMastery[prereq] || 0) >= 0.6
      );
      
      if (!prerequisitesMet) continue;
      
      // Calculate recommendation score
      let score = 1 - currentMastery; // Prefer unlearned concepts
      score *= this.getComplexityMatch(concept.difficulty, userVector.learningStyle.preferredComplexity);
      score *= this.getTopicPreferenceMatch(concept.category, userVector.learningStyle.topicPreferences);
      
      recommendations.push({ conceptId, score });
    }

    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 5)
      .map(r => r.conceptId);
  }

  private getComplexityMatch(conceptDifficulty: number, preferredComplexity: string): number {
    const complexityMap = { beginner: 3, intermediate: 6, advanced: 9 };
    const preferred = complexityMap[preferredComplexity];
    return 1 - Math.abs(conceptDifficulty - preferred) / 10;
  }

  private getTopicPreferenceMatch(category: string, preferences: string[]): number {
    if (preferences.length === 0) return 1;
    return preferences.includes(category) ? 1.2 : 0.8;
  }

  generateDynamicConceptMap(userId: string): any {
    const userVector = this.userVectors.get(userId);
    if (!userVector) return null;

    const nodes = [];
    const edges = [];

    for (const [conceptId, concept] of this.conceptGraph) {
      const mastery = userVector.conceptMastery[conceptId] || 0;
      const isUnlocked = concept.prerequisites.every(
        prereq => (userVector.conceptMastery[prereq] || 0) >= 0.6
      );

      nodes.push({
        id: conceptId,
        label: concept.name,
        mastery,
        difficulty: concept.difficulty,
        category: concept.category,
        isUnlocked,
        size: 10 + mastery * 20,
        color: this.getCategoryColor(concept.category, mastery)
      });

      // Add edges for prerequisites and related concepts
      concept.prerequisites.forEach(prereq => {
        edges.push({
          from: prereq,
          to: conceptId,
          type: 'prerequisite',
          strength: 0.8
        });
      });

      concept.relatedConcepts.forEach(related => {
        if (this.conceptGraph.has(related)) {
          edges.push({
            from: conceptId,
            to: related,
            type: 'related',
            strength: 0.4
          });
        }
      });
    }

    return { nodes, edges };
  }

  private getCategoryColor(category: string, mastery: number): string {
    const baseColors = {
      investing: '#3B82F6',
      risk: '#EF4444',
      planning: '#10B981',
      analysis: '#F59E0B',
      behavioral: '#8B5CF6'
    };
    
    const baseColor = baseColors[category] || '#6B7280';
    const opacity = 0.3 + mastery * 0.7;
    return `${baseColor}${Math.round(opacity * 255).toString(16)}`;
  }

  private async saveUserVector(userId: string, userVector: UserVector): Promise<void> {
    const serialized = this.serializeUserVector(userVector);
    
    const { error } = await supabase
      .from('user_vectors')
      .upsert({
        user_id: userId,
        ...serialized,
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Error saving user vector:', error);
    }
  }

  private serializeUserVector(userVector: UserVector): any {
    return {
      concept_mastery: JSON.stringify(userVector.conceptMastery),
      risk_profile: JSON.stringify(userVector.riskProfile),
      learning_style: JSON.stringify(userVector.learningStyle),
      behavioral_patterns: JSON.stringify(userVector.behavioralPatterns),
      concept_connections: JSON.stringify(userVector.conceptConnections),
      progress_history: JSON.stringify(userVector.progressHistory)
    };
  }

  private deserializeUserVector(data: any): UserVector {
    return {
      userId: data.user_id,
      conceptMastery: JSON.parse(data.concept_mastery || '{}'),
      riskProfile: JSON.parse(data.risk_profile || '{}'),
      learningStyle: JSON.parse(data.learning_style || '{}'),
      behavioralPatterns: JSON.parse(data.behavioral_patterns || '{}'),
      conceptConnections: JSON.parse(data.concept_connections || '{}'),
      progressHistory: JSON.parse(data.progress_history || '[]')
    };
  }
}

export const memorySystem = new MemorySystem();