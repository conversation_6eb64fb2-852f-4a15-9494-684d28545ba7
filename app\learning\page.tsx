"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  BookOpen, 
  Target, 
  Trophy, 
  Clock, 
  CheckCircle,
  Play,
  Lock,
  Star,
  TrendingUp,
  PieChart,
  DollarSign,
  Shield
} from 'lucide-react';

interface LearningModule {
  id: string;
  title: string;
  description: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  duration: string;
  progress: number;
  isUnlocked: boolean;
  isCompleted: boolean;
  lessons: number;
  category: string;
  icon: React.ReactNode;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  isUnlocked: boolean;
  icon: React.ReactNode;
  unlockedAt?: Date;
}

export default function LearningPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const learningModules: LearningModule[] = [
    {
      id: '1',
      title: 'Investment Fundamentals',
      description: 'Learn the basics of investing, from stocks and bonds to risk and return principles.',
      difficulty: 'Beginner',
      duration: '2 hours',
      progress: 100,
      isUnlocked: true,
      isCompleted: true,
      lessons: 8,
      category: 'investing',
      icon: <TrendingUp className="h-5 w-5" />
    },
    {
      id: '2',
      title: 'Portfolio Diversification',
      description: 'Master the art of building a balanced portfolio that minimizes risk while maximizing returns.',
      difficulty: 'Intermediate',
      duration: '1.5 hours',
      progress: 75,
      isUnlocked: true,
      isCompleted: false,
      lessons: 6,
      category: 'investing',
      icon: <PieChart className="h-5 w-5" />
    },
    {
      id: '3',
      title: 'Personal Budgeting',
      description: 'Create and maintain a budget that helps you reach your financial goals.',
      difficulty: 'Beginner',
      duration: '1 hour',
      progress: 90,
      isUnlocked: true,
      isCompleted: false,
      lessons: 5,
      category: 'budgeting',
      icon: <DollarSign className="h-5 w-5" />
    },
    {
      id: '4',
      title: 'Risk Management',
      description: 'Understand different types of financial risks and how to protect your wealth.',
      difficulty: 'Intermediate',
      duration: '2.5 hours',
      progress: 40,
      isUnlocked: true,
      isCompleted: false,
      lessons: 10,
      category: 'risk',
      icon: <Shield className="h-5 w-5" />
    },
    {
      id: '5',
      title: 'Advanced Tax Strategies',
      description: 'Optimize your tax situation with advanced planning techniques and strategies.',
      difficulty: 'Advanced',
      duration: '3 hours',
      progress: 0,
      isUnlocked: false,
      isCompleted: false,
      lessons: 12,
      category: 'tax',
      icon: <Target className="h-5 w-5" />
    },
    {
      id: '6',
      title: 'Retirement Planning',
      description: 'Plan for a secure retirement with 401(k)s, IRAs, and other retirement vehicles.',
      difficulty: 'Intermediate',
      duration: '2 hours',
      progress: 60,
      isUnlocked: true,
      isCompleted: false,
      lessons: 8,
      category: 'retirement',
      icon: <Clock className="h-5 w-5" />
    }
  ];

  const achievements: Achievement[] = [
    {
      id: '1',
      title: 'First Steps',
      description: 'Complete your first learning module',
      isUnlocked: true,
      icon: <BookOpen className="h-5 w-5" />,
      unlockedAt: new Date('2024-01-15')
    },
    {
      id: '2',
      title: 'Investment Rookie',
      description: 'Finish the Investment Fundamentals course',
      isUnlocked: true,
      icon: <TrendingUp className="h-5 w-5" />,
      unlockedAt: new Date('2024-01-20')
    },
    {
      id: '3',
      title: 'Diversification Master',
      description: 'Complete the Portfolio Diversification module',
      isUnlocked: false,
      icon: <PieChart className="h-5 w-5" />
    },
    {
      id: '4',
      title: 'Budget Boss',
      description: 'Master personal budgeting techniques',
      isUnlocked: false,
      icon: <DollarSign className="h-5 w-5" />
    },
    {
      id: '5',
      title: 'Risk Manager',
      description: 'Complete all risk management courses',
      isUnlocked: false,
      icon: <Shield className="h-5 w-5" />
    },
    {
      id: '6',
      title: 'Learning Streak',
      description: 'Complete lessons for 7 days in a row',
      isUnlocked: false,
      icon: <Star className="h-5 w-5" />
    }
  ];

  const learningStats = {
    totalModules: learningModules.length,
    completedModules: learningModules.filter(m => m.isCompleted).length,
    totalTime: learningModules.reduce((acc, m) => acc + parseFloat(m.duration), 0),
    averageProgress: Math.round(
      learningModules.reduce((acc, m) => acc + m.progress, 0) / learningModules.length
    ),
    unlockedAchievements: achievements.filter(a => a.isUnlocked).length
  };

  const filteredModules = learningModules.filter(module => 
    selectedCategory === 'all' || module.category === selectedCategory
  );

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-500/10 text-green-300 border-green-500/30';
      case 'Intermediate': return 'bg-yellow-500/10 text-yellow-300 border-yellow-500/30';
      case 'Advanced': return 'bg-red-500/10 text-red-300 border-red-500/30';
      default: return 'bg-slate-500/10 text-slate-300 border-slate-500/30';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold text-white">Adaptive Learning Center</h1>
            <p className="text-slate-300 max-w-2xl mx-auto">
              Personalized financial education that adapts to your knowledge level and learning pace. 
              Build expertise through interactive modules and track your progress.
            </p>
          </div>

          {/* Learning Stats */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <BookOpen className="h-5 w-5 text-blue-400" />
                </div>
                <p className="text-2xl font-bold text-white">{learningStats.completedModules}/{learningStats.totalModules}</p>
                <p className="text-xs text-slate-400">Modules Completed</p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Brain className="h-5 w-5 text-green-400" />
                </div>
                <p className="text-2xl font-bold text-white">{learningStats.averageProgress}%</p>
                <p className="text-xs text-slate-400">Average Progress</p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Clock className="h-5 w-5 text-purple-400" />
                </div>
                <p className="text-2xl font-bold text-white">{learningStats.totalTime}h</p>
                <p className="text-xs text-slate-400">Total Time</p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Trophy className="h-5 w-5 text-yellow-400" />
                </div>
                <p className="text-2xl font-bold text-white">{learningStats.unlockedAchievements}</p>
                <p className="text-xs text-slate-400">Achievements</p>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-4 text-center">
                <div className="w-10 h-10 bg-indigo-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Star className="h-5 w-5 text-indigo-400" />
                </div>
                <p className="text-2xl font-bold text-white">7</p>
                <p className="text-xs text-slate-400">Day Streak</p>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <Tabs defaultValue="modules" className="space-y-6">
            <TabsList className="bg-slate-800 border-slate-700">
              <TabsTrigger value="modules" className="data-[state=active]:bg-slate-700">
                Learning Modules
              </TabsTrigger>
              <TabsTrigger value="achievements" className="data-[state=active]:bg-slate-700">
                Achievements
              </TabsTrigger>
              <TabsTrigger value="progress" className="data-[state=active]:bg-slate-700">
                Progress Tracking
              </TabsTrigger>
            </TabsList>

            <TabsContent value="modules" className="space-y-6">
              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedCategory === 'all' ? 'secondary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('all')}
                  className="border-slate-600"
                >
                  All Categories
                </Button>
                <Button
                  variant={selectedCategory === 'investing' ? 'secondary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('investing')}
                  className="border-slate-600"
                >
                  Investing
                </Button>
                <Button
                  variant={selectedCategory === 'budgeting' ? 'secondary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('budgeting')}
                  className="border-slate-600"
                >
                  Budgeting
                </Button>
                <Button
                  variant={selectedCategory === 'risk' ? 'secondary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('risk')}
                  className="border-slate-600"
                >
                  Risk Management
                </Button>
                <Button
                  variant={selectedCategory === 'retirement' ? 'secondary' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory('retirement')}
                  className="border-slate-600"
                >
                  Retirement
                </Button>
              </div>

              {/* Modules Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredModules.map((module) => (
                  <Card key={module.id} className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm transition-all duration-300 ${
                    module.isUnlocked ? 'hover:bg-slate-800/70' : 'opacity-60'
                  }`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                            {module.icon}
                          </div>
                          {module.isCompleted && (
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          )}
                          {!module.isUnlocked && (
                            <Lock className="h-4 w-4 text-slate-500" />
                          )}
                        </div>
                        <Badge variant="outline" className={getDifficultyColor(module.difficulty)}>
                          {module.difficulty}
                        </Badge>
                      </div>
                      <CardTitle className="text-white text-lg">{module.title}</CardTitle>
                      <CardDescription className="text-slate-400">
                        {module.description}
                      </CardDescription>
                    </CardHeader>
                    
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">{module.lessons} lessons</span>
                        <span className="text-slate-400">{module.duration}</span>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-300">Progress</span>
                          <span className="text-sm text-white">{module.progress}%</span>
                        </div>
                        <Progress value={module.progress} className="h-2" />
                      </div>
                      
                      <Button 
                        className="w-full" 
                        disabled={!module.isUnlocked}
                        variant={module.isUnlocked ? "default" : "secondary"}
                      >
                        {!module.isUnlocked ? (
                          <>
                            <Lock className="h-4 w-4 mr-2" />
                            Locked
                          </>
                        ) : module.isCompleted ? (
                          <>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Review
                          </>
                        ) : (
                          <>
                            <Play className="h-4 w-4 mr-2" />
                            Continue
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="achievements" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {achievements.map((achievement) => (
                  <Card key={achievement.id} className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm ${
                    achievement.isUnlocked ? 'ring-2 ring-yellow-500/30' : 'opacity-60'
                  }`}>
                    <CardContent className="p-6 text-center space-y-4">
                      <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto ${
                        achievement.isUnlocked ? 'bg-yellow-500/20' : 'bg-slate-600/20'
                      }`}>
                        <div className={achievement.isUnlocked ? 'text-yellow-400' : 'text-slate-500'}>
                          {achievement.icon}
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold text-white">{achievement.title}</h3>
                        <p className="text-sm text-slate-400">{achievement.description}</p>
                      </div>
                      
                      {achievement.isUnlocked ? (
                        <div className="space-y-2">
                          <Badge variant="outline" className="bg-yellow-500/10 text-yellow-300 border-yellow-500/30">
                            <Trophy className="h-3 w-3 mr-1" />
                            Unlocked
                          </Badge>
                          {achievement.unlockedAt && (
                            <p className="text-xs text-slate-500">
                              {achievement.unlockedAt.toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      ) : (
                        <Badge variant="outline" className="bg-slate-500/10 text-slate-400 border-slate-500/30">
                          <Lock className="h-3 w-3 mr-1" />
                          Locked
                        </Badge>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="progress" className="space-y-6">
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">Learning Analytics</CardTitle>
                  <CardDescription className="text-slate-400">
                    Track your learning journey and identify areas for improvement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-white">Module Progress</h3>
                      {learningModules.map((module) => (
                        <div key={module.id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-slate-300">{module.title}</span>
                            <span className="text-sm text-white">{module.progress}%</span>
                          </div>
                          <Progress value={module.progress} className="h-2" />
                        </div>
                      ))}
                    </div>
                    
                    <div className="space-y-6">
                      <h3 className="text-lg font-semibold text-white">Strengths & Recommendations</h3>
                      <div className="space-y-4">
                        <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/30">
                          <h4 className="text-sm font-medium text-green-300 mb-2">Strong Areas</h4>
                          <ul className="text-sm text-slate-300 space-y-1">
                            <li>• Investment fundamentals</li>
                            <li>• Basic budgeting concepts</li>
                            <li>• Risk awareness</li>
                          </ul>
                        </div>
                        
                        <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/30">
                          <h4 className="text-sm font-medium text-yellow-300 mb-2">Areas for Growth</h4>
                          <ul className="text-sm text-slate-300 space-y-1">
                            <li>• Advanced portfolio theory</li>
                            <li>• Tax optimization strategies</li>
                            <li>• Estate planning basics</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}