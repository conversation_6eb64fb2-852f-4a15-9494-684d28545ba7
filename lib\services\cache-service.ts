// Intelligent Caching Service for Performance and Memory
export interface CacheConfig {
  defaultTTL: number;
  maxSize: number;
  compressionThreshold: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  compressed: boolean;
}

export class CacheService {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private config: CacheConfig;
  private cleanupInterval: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 3600, // 1 hour
      maxSize: 1000,
      compressionThreshold: 10000, // 10KB
      ...config
    };

    // Start cleanup process
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 300000); // Every 5 minutes
  }

  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const now = Date.now();
    const entryTTL = ttl || this.config.defaultTTL;
    
    // Check if data should be compressed
    const serialized = JSON.stringify(data);
    const shouldCompress = serialized.length > this.config.compressionThreshold;
    
    const entry: CacheEntry<T> = {
      data: shouldCompress ? this.compress(serialized) : data,
      timestamp: now,
      ttl: entryTTL * 1000, // Convert to milliseconds
      accessCount: 0,
      lastAccessed: now,
      compressed: shouldCompress
    };

    this.cache.set(key, entry);
    
    // Enforce size limit
    if (this.cache.size > this.config.maxSize) {
      this.evictLeastUsed();
    }

    // For production, also store in Redis/Supabase
    await this.persistToExternalCache(key, entry);
  }

  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      // Try to load from external cache
      const externalEntry = await this.loadFromExternalCache<T>(key);
      if (externalEntry) {
        this.cache.set(key, externalEntry);
        return this.processEntry(externalEntry);
      }
      return null;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      await this.removeFromExternalCache(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return this.processEntry(entry);
  }

  private processEntry<T>(entry: CacheEntry<T>): T {
    if (entry.compressed) {
      const decompressed = this.decompress(entry.data as string);
      return JSON.parse(decompressed);
    }
    return entry.data;
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key);
    await this.removeFromExternalCache(key);
  }

  async clear(): Promise<void> {
    this.cache.clear();
    await this.clearExternalCache();
  }

  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      this.removeFromExternalCache(key);
    });
  }

  private evictLeastUsed(): void {
    let leastUsedKey = '';
    let leastUsedScore = Infinity;

    for (const [key, entry] of this.cache) {
      // Score based on access frequency and recency
      const score = entry.accessCount / (Date.now() - entry.lastAccessed + 1);
      if (score < leastUsedScore) {
        leastUsedScore = score;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.removeFromExternalCache(leastUsedKey);
    }
  }

  private compress(data: string): string {
    // Simple compression simulation - in production use actual compression
    return btoa(data);
  }

  private decompress(data: string): string {
    // Simple decompression simulation
    return atob(data);
  }

  // External cache methods (Redis/Supabase integration)
  private async persistToExternalCache(key: string, entry: CacheEntry<any>): Promise<void> {
    try {
      // In production, implement Redis or Supabase caching
      if (typeof window === 'undefined') {
        // Server-side: use Redis
        // await redis.setex(key, entry.ttl / 1000, JSON.stringify(entry));
      } else {
        // Client-side: use localStorage with size limits
        if (this.shouldPersistLocally(entry)) {
          localStorage.setItem(`cache_${key}`, JSON.stringify(entry));
        }
      }
    } catch (error) {
      console.warn('Failed to persist to external cache:', error);
    }
  }

  private async loadFromExternalCache<T>(key: string): Promise<CacheEntry<T> | null> {
    try {
      if (typeof window === 'undefined') {
        // Server-side: load from Redis
        // const data = await redis.get(key);
        // return data ? JSON.parse(data) : null;
        return null;
      } else {
        // Client-side: load from localStorage
        const data = localStorage.getItem(`cache_${key}`);
        if (data) {
          const entry = JSON.parse(data);
          if (!this.isExpired(entry)) {
            return entry;
          } else {
            localStorage.removeItem(`cache_${key}`);
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load from external cache:', error);
    }
    return null;
  }

  private async removeFromExternalCache(key: string): Promise<void> {
    try {
      if (typeof window === 'undefined') {
        // Server-side: remove from Redis
        // await redis.del(key);
      } else {
        // Client-side: remove from localStorage
        localStorage.removeItem(`cache_${key}`);
      }
    } catch (error) {
      console.warn('Failed to remove from external cache:', error);
    }
  }

  private async clearExternalCache(): Promise<void> {
    try {
      if (typeof window === 'undefined') {
        // Server-side: clear Redis
        // await redis.flushdb();
      } else {
        // Client-side: clear localStorage cache entries
        const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'));
        keys.forEach(key => localStorage.removeItem(key));
      }
    } catch (error) {
      console.warn('Failed to clear external cache:', error);
    }
  }

  private shouldPersistLocally(entry: CacheEntry<any>): boolean {
    // Only persist smaller, frequently accessed items locally
    const serialized = JSON.stringify(entry);
    return serialized.length < 50000 && entry.accessCount > 1; // 50KB limit
  }

  // Specialized cache methods for different data types
  async cacheResponse(key: string, response: any, ttl?: number): Promise<void> {
    await this.set(`response_${key}`, response, ttl);
  }

  async getCachedResponse<T>(key: string): Promise<T | null> {
    return await this.get<T>(`response_${key}`);
  }

  async cacheUserVector(userId: string, vector: any, ttl?: number): Promise<void> {
    await this.set(`user_vector_${userId}`, vector, ttl || 7200); // 2 hours
  }

  async getCachedUserVector(userId: string): Promise<any | null> {
    return await this.get(`user_vector_${userId}`);
  }

  async saveContext(contextKey: string, context: any, ttl?: number): Promise<void> {
    await this.set(`context_${contextKey}`, context, ttl || 86400); // 24 hours
  }

  async getContext(contextKey: string): Promise<any | null> {
    return await this.get(`context_${contextKey}`);
  }

  // Analytics and monitoring
  getCacheStats(): {
    size: number;
    hitRate: number;
    memoryUsage: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    let totalAccesses = 0;
    let totalHits = 0;
    let oldestTimestamp = Date.now();
    let newestTimestamp = 0;
    let memoryUsage = 0;

    for (const [key, entry] of this.cache) {
      totalAccesses += entry.accessCount;
      if (entry.accessCount > 0) totalHits++;
      
      if (entry.timestamp < oldestTimestamp) oldestTimestamp = entry.timestamp;
      if (entry.timestamp > newestTimestamp) newestTimestamp = entry.timestamp;
      
      memoryUsage += JSON.stringify(entry).length;
    }

    return {
      size: this.cache.size,
      hitRate: totalAccesses > 0 ? totalHits / totalAccesses : 0,
      memoryUsage,
      oldestEntry: oldestTimestamp,
      newestEntry: newestTimestamp
    };
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cache.clear();
  }
}

// Export singleton instance
export const cacheService = new CacheService({
  defaultTTL: 3600, // 1 hour
  maxSize: 2000,
  compressionThreshold: 5000 // 5KB
});