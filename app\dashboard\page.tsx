"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  PieChart, 
  Target, 
  Brain,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, PieChart as RechartsPieChart, Cell, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

export default function DashboardPage() {
  const [financialScore, setFinancialScore] = useState(0);

  useEffect(() => {
    // Animate financial score
    const timer = setTimeout(() => setFinancialScore(78), 500);
    return () => clearTimeout(timer);
  }, []);

  const portfolioData = [
    { month: 'Jan', value: 10000, growth: 2.5 },
    { month: 'Feb', value: 10250, growth: 3.1 },
    { month: 'Mar', value: 10567, growth: 1.8 },
    { month: 'Apr', value: 10890, growth: 4.2 },
    { month: 'May', value: 11200, growth: 2.9 },
    { month: 'Jun', value: 11650, growth: 3.7 }
  ];

  const allocationData = [
    { name: 'Stocks', value: 60, color: '#3B82F6' },
    { name: 'Bonds', value: 25, color: '#10B981' },
    { name: 'ETFs', value: 10, color: '#F59E0B' },
    { name: 'Cash', value: 5, color: '#6B7280' }
  ];

  const learningProgress = [
    { topic: 'Investment Basics', progress: 85, completed: true },
    { topic: 'Risk Management', progress: 65, completed: false },
    { topic: 'Tax Strategies', progress: 40, completed: false },
    { topic: 'Retirement Planning', progress: 90, completed: true }
  ];

  const insights = [
    {
      type: 'opportunity',
      title: 'Rebalancing Recommendation',
      description: 'Your equity allocation is 5% above target. Consider rebalancing.',
      priority: 'medium'
    },
    {
      type: 'warning',
      title: 'Emergency Fund Alert',
      description: 'Your emergency fund covers only 4 months of expenses. Aim for 6 months.',
      priority: 'high'
    },
    {
      type: 'success',
      title: 'Savings Goal Achievement',
      description: 'Congratulations! You\'ve reached 78% of your annual savings goal.',
      priority: 'low'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-white">Financial Dashboard</h1>
              <p className="text-slate-300">Your complete financial overview and insights</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                <Clock className="h-3 w-3 mr-1" />
                Last updated: 2 min ago
              </Badge>
            </div>
          </div>

          {/* Financial Health Score */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-400" />
                Financial Health Score
              </CardTitle>
              <CardDescription className="text-slate-400">
                AI-powered assessment of your overall financial wellness
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-8">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-2xl font-bold text-white">{financialScore}/100</span>
                    <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                      Good
                    </Badge>
                  </div>
                  <Progress value={financialScore} className="h-3" />
                  <p className="text-sm text-slate-400 mt-2">
                    You're doing well! Focus on emergency fund and diversification to reach excellent.
                  </p>
                </div>
                <div className="hidden sm:block">
                  <div className="w-24 h-24 rounded-full border-4 border-green-500 flex items-center justify-center">
                    <span className="text-2xl font-bold text-green-400">{financialScore}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Total Portfolio</p>
                    <p className="text-2xl font-bold text-white">$11,650</p>
                    <p className="text-sm text-green-400 flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      +16.5% this year
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-6 w-6 text-blue-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Monthly Savings</p>
                    <p className="text-2xl font-bold text-white">$1,200</p>
                    <p className="text-sm text-green-400 flex items-center gap-1">
                      <TrendingUp className="h-3 w-3" />
                      +8.2% vs target
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Target className="h-6 w-6 text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Risk Level</p>
                    <p className="text-2xl font-bold text-white">Moderate</p>
                    <p className="text-sm text-slate-400">
                      Volatility: 12.3%
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <PieChart className="h-6 w-6 text-yellow-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Learning Progress</p>
                    <p className="text-2xl font-bold text-white">70%</p>
                    <p className="text-sm text-slate-400">
                      4 modules completed
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Brain className="h-6 w-6 text-purple-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="portfolio" className="space-y-6">
            <TabsList className="bg-slate-800 border-slate-700">
              <TabsTrigger value="portfolio" className="data-[state=active]:bg-slate-700">Portfolio</TabsTrigger>
              <TabsTrigger value="insights" className="data-[state=active]:bg-slate-700">AI Insights</TabsTrigger>
              <TabsTrigger value="learning" className="data-[state=active]:bg-slate-700">Learning</TabsTrigger>
            </TabsList>

            <TabsContent value="portfolio" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white">Portfolio Growth</CardTitle>
                    <CardDescription className="text-slate-400">
                      6-month performance overview
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={portfolioData}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis dataKey="month" stroke="#9CA3AF" />
                        <YAxis stroke="#9CA3AF" />
                        <Tooltip 
                          contentStyle={{ 
                            backgroundColor: '#1F2937', 
                            border: '1px solid #374151',
                            borderRadius: '8px'
                          }} 
                        />
                        <Area 
                          type="monotone" 
                          dataKey="value" 
                          stroke="#3B82F6" 
                          fill="#3B82F6" 
                          fillOpacity={0.1}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white">Asset Allocation</CardTitle>
                    <CardDescription className="text-slate-400">
                      Current portfolio distribution
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsPieChart>
                        <Pie
                          dataKey="value"
                          data={allocationData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                        >
                          {allocationData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="insights" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {insights.map((insight, index) => (
                  <Card key={index} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {insight.type === 'warning' && (
                            <AlertTriangle className="h-5 w-5 text-yellow-400" />
                          )}
                          {insight.type === 'success' && (
                            <CheckCircle className="h-5 w-5 text-green-400" />
                          )}
                          {insight.type === 'opportunity' && (
                            <TrendingUp className="h-5 w-5 text-blue-400" />
                          )}
                        </div>
                        <Badge 
                          variant="outline" 
                          className={`${
                            insight.priority === 'high' ? 'border-red-500/30 text-red-300' :
                            insight.priority === 'medium' ? 'border-yellow-500/30 text-yellow-300' :
                            'border-green-500/30 text-green-300'
                          }`}
                        >
                          {insight.priority}
                        </Badge>
                      </div>
                      <CardTitle className="text-white text-sm">{insight.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-slate-400">{insight.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="learning" className="space-y-6">
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white">Learning Progress</CardTitle>
                  <CardDescription className="text-slate-400">
                    Track your financial education journey
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {learningProgress.map((item, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium text-white">{item.topic}</span>
                            {item.completed && (
                              <CheckCircle className="h-4 w-4 text-green-400" />
                            )}
                          </div>
                          <span className="text-sm text-slate-400">{item.progress}%</span>
                        </div>
                        <Progress value={item.progress} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}