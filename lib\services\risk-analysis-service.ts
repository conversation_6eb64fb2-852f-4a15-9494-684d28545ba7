// Advanced Risk Analysis Service
import { memorySystem } from './memory-system';

export interface RiskMetrics {
  var95: number; // 95% Value at Risk
  var99: number; // 99% Value at Risk
  expectedShortfall: number; // Conditional VaR
  sharpeRatio: number;
  sortinoRatio: number;
  maxDrawdown: number;
  volatility: number;
  beta: number;
  alpha: number;
  informationRatio: number;
}

export interface PortfolioPosition {
  symbol: string;
  weight: number;
  expectedReturn: number;
  volatility: number;
  beta: number;
}

export interface RiskScenario {
  name: string;
  description: string;
  marketShock: number; // Percentage market decline
  probability: number; // Probability of occurrence
  timeHorizon: number; // Days
  impact: {
    portfolioLoss: number;
    recoveryTime: number;
    actionRequired: string[];
  };
}

export interface RiskAssessment {
  overallRiskScore: number; // 1-10 scale
  riskMetrics: RiskMetrics;
  scenarios: RiskScenario[];
  recommendations: string[];
  alerts: Array<{
    severity: 'low' | 'medium' | 'high';
    message: string;
    action: string;
  }>;
  diversificationScore: number;
  concentrationRisks: string[];
}

export class RiskAnalysisService {
  private correlationMatrix: Map<string, Map<string, number>> = new Map();
  private marketData: Map<string, any> = new Map();

  constructor() {
    this.initializeCorrelationMatrix();
    this.initializeMarketData();
  }

  private initializeCorrelationMatrix() {
    // Simplified correlation matrix for major asset classes
    const assets = ['stocks', 'bonds', 'reits', 'commodities', 'international'];
    const correlations = [
      [1.00, -0.20, 0.70, 0.30, 0.85], // stocks
      [-0.20, 1.00, -0.10, -0.15, -0.25], // bonds
      [0.70, -0.10, 1.00, 0.40, 0.60], // reits
      [0.30, -0.15, 0.40, 1.00, 0.25], // commodities
      [0.85, -0.25, 0.60, 0.25, 1.00]  // international
    ];

    assets.forEach((asset1, i) => {
      const correlationMap = new Map<string, number>();
      assets.forEach((asset2, j) => {
        correlationMap.set(asset2, correlations[i][j]);
      });
      this.correlationMatrix.set(asset1, correlationMap);
    });
  }

  private initializeMarketData() {
    // Historical market data for risk calculations
    this.marketData.set('market_volatility', 0.16); // 16% annual volatility
    this.marketData.set('risk_free_rate', 0.045); // 4.5% risk-free rate
    this.marketData.set('market_return', 0.10); // 10% expected market return
  }

  async analyzePortfolioRisk(
    positions: PortfolioPosition[],
    userId: string,
    timeHorizon: number = 252 // Trading days in a year
  ): Promise<RiskAssessment> {
    
    const userVector = await memorySystem.getUserVector(userId);
    
    // Calculate portfolio-level metrics
    const portfolioMetrics = this.calculatePortfolioMetrics(positions);
    const riskMetrics = this.calculateRiskMetrics(positions, portfolioMetrics);
    
    // Generate risk scenarios
    const scenarios = this.generateRiskScenarios(positions, userVector);
    
    // Assess diversification
    const diversificationScore = this.calculateDiversificationScore(positions);
    const concentrationRisks = this.identifyConcentrationRisks(positions);
    
    // Generate recommendations
    const recommendations = this.generateRiskRecommendations(
      riskMetrics,
      diversificationScore,
      userVector
    );
    
    // Create alerts
    const alerts = this.generateRiskAlerts(riskMetrics, userVector);
    
    // Calculate overall risk score
    const overallRiskScore = this.calculateOverallRiskScore(
      riskMetrics,
      diversificationScore,
      userVector
    );

    return {
      overallRiskScore,
      riskMetrics,
      scenarios,
      recommendations,
      alerts,
      diversificationScore,
      concentrationRisks
    };
  }

  private calculatePortfolioMetrics(positions: PortfolioPosition[]) {
    const portfolioReturn = positions.reduce((sum, pos) => 
      sum + pos.weight * pos.expectedReturn, 0
    );
    
    const portfolioVariance = this.calculatePortfolioVariance(positions);
    const portfolioVolatility = Math.sqrt(portfolioVariance);
    
    const portfolioBeta = positions.reduce((sum, pos) => 
      sum + pos.weight * pos.beta, 0
    );

    return {
      expectedReturn: portfolioReturn,
      volatility: portfolioVolatility,
      variance: portfolioVariance,
      beta: portfolioBeta
    };
  }

  private calculatePortfolioVariance(positions: PortfolioPosition[]): number {
    let variance = 0;
    
    // Individual asset variances
    for (const pos of positions) {
      variance += Math.pow(pos.weight, 2) * Math.pow(pos.volatility, 2);
    }
    
    // Covariance terms (simplified)
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const correlation = this.getAssetCorrelation(positions[i].symbol, positions[j].symbol);
        const covariance = correlation * positions[i].volatility * positions[j].volatility;
        variance += 2 * positions[i].weight * positions[j].weight * covariance;
      }
    }
    
    return variance;
  }

  private getAssetCorrelation(asset1: string, asset2: string): number {
    // Simplified asset class mapping
    const assetClassMap = {
      'SPY': 'stocks', 'QQQ': 'stocks', 'IWM': 'stocks',
      'TLT': 'bonds', 'IEF': 'bonds',
      'VNQ': 'reits',
      'GLD': 'commodities', 'DBC': 'commodities',
      'VEA': 'international', 'VWO': 'international'
    };
    
    const class1 = assetClassMap[asset1] || 'stocks';
    const class2 = assetClassMap[asset2] || 'stocks';
    
    return this.correlationMatrix.get(class1)?.get(class2) || 0.3;
  }

  private calculateRiskMetrics(positions: PortfolioPosition[], portfolioMetrics: any): RiskMetrics {
    const riskFreeRate = this.marketData.get('risk_free_rate');
    const marketReturn = this.marketData.get('market_return');
    const marketVolatility = this.marketData.get('market_volatility');
    
    // Value at Risk calculations (parametric approach)
    const var95 = portfolioMetrics.volatility * 1.645; // 95% confidence
    const var99 = portfolioMetrics.volatility * 2.326; // 99% confidence
    
    // Expected Shortfall (simplified)
    const expectedShortfall = var95 * 1.3;
    
    // Sharpe Ratio
    const sharpeRatio = (portfolioMetrics.expectedReturn - riskFreeRate) / portfolioMetrics.volatility;
    
    // Sortino Ratio (simplified - using total volatility)
    const sortinoRatio = sharpeRatio * 1.2; // Approximation
    
    // Maximum Drawdown (estimated)
    const maxDrawdown = portfolioMetrics.volatility * 2.5;
    
    // Alpha calculation
    const expectedReturnFromBeta = riskFreeRate + portfolioMetrics.beta * (marketReturn - riskFreeRate);
    const alpha = portfolioMetrics.expectedReturn - expectedReturnFromBeta;
    
    // Information Ratio (simplified)
    const trackingError = Math.abs(portfolioMetrics.volatility - marketVolatility);
    const informationRatio = alpha / (trackingError || 0.01);

    return {
      var95,
      var99,
      expectedShortfall,
      sharpeRatio,
      sortinoRatio,
      maxDrawdown,
      volatility: portfolioMetrics.volatility,
      beta: portfolioMetrics.beta,
      alpha,
      informationRatio
    };
  }

  private generateRiskScenarios(positions: PortfolioPosition[], userVector: any): RiskScenario[] {
    const scenarios: RiskScenario[] = [
      {
        name: 'Market Correction',
        description: '10-15% market decline over 1-3 months',
        marketShock: -0.125,
        probability: 0.15,
        timeHorizon: 60,
        impact: {
          portfolioLoss: this.calculateScenarioImpact(positions, -0.125),
          recoveryTime: 180,
          actionRequired: ['Review risk tolerance', 'Consider rebalancing', 'Maintain emergency fund']
        }
      },
      {
        name: 'Bear Market',
        description: '20%+ market decline over 6-18 months',
        marketShock: -0.30,
        probability: 0.05,
        timeHorizon: 365,
        impact: {
          portfolioLoss: this.calculateScenarioImpact(positions, -0.30),
          recoveryTime: 720,
          actionRequired: ['Reduce equity exposure', 'Increase cash position', 'Review investment timeline']
        }
      },
      {
        name: 'Interest Rate Shock',
        description: 'Rapid 2% increase in interest rates',
        marketShock: -0.08,
        probability: 0.10,
        timeHorizon: 30,
        impact: {
          portfolioLoss: this.calculateScenarioImpact(positions, -0.08, 'bonds'),
          recoveryTime: 365,
          actionRequired: ['Reduce duration risk', 'Consider floating rate bonds', 'Review bond allocation']
        }
      }
    ];

    return scenarios;
  }

  private calculateScenarioImpact(
    positions: PortfolioPosition[], 
    marketShock: number, 
    assetClass?: string
  ): number {
    let totalImpact = 0;
    
    for (const position of positions) {
      let positionImpact = marketShock * position.beta;
      
      // Adjust for specific asset class shocks
      if (assetClass === 'bonds' && position.symbol.includes('TLT')) {
        positionImpact *= 2; // Bonds more sensitive to rate changes
      }
      
      totalImpact += position.weight * positionImpact;
    }
    
    return totalImpact;
  }

  private calculateDiversificationScore(positions: PortfolioPosition[]): number {
    // Calculate effective number of positions
    const sumOfSquaredWeights = positions.reduce((sum, pos) => 
      sum + Math.pow(pos.weight, 2), 0
    );
    
    const effectivePositions = 1 / sumOfSquaredWeights;
    const maxScore = Math.min(effectivePositions / 10, 1); // Normalize to 0-1
    
    // Adjust for correlation
    const avgCorrelation = this.calculateAverageCorrelation(positions);
    const correlationPenalty = avgCorrelation * 0.3;
    
    return Math.max(0, maxScore - correlationPenalty);
  }

  private calculateAverageCorrelation(positions: PortfolioPosition[]): number {
    let totalCorrelation = 0;
    let pairCount = 0;
    
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        totalCorrelation += Math.abs(this.getAssetCorrelation(
          positions[i].symbol, 
          positions[j].symbol
        ));
        pairCount++;
      }
    }
    
    return pairCount > 0 ? totalCorrelation / pairCount : 0;
  }

  private identifyConcentrationRisks(positions: PortfolioPosition[]): string[] {
    const risks: string[] = [];
    
    // Single position concentration
    const largePositions = positions.filter(pos => pos.weight > 0.20);
    if (largePositions.length > 0) {
      risks.push(`High concentration in ${largePositions.map(p => p.symbol).join(', ')}`);
    }
    
    // Sector concentration (simplified)
    const techPositions = positions.filter(pos => 
      ['QQQ', 'AAPL', 'MSFT', 'GOOGL'].includes(pos.symbol)
    );
    const techWeight = techPositions.reduce((sum, pos) => sum + pos.weight, 0);
    
    if (techWeight > 0.40) {
      risks.push('High technology sector concentration');
    }
    
    return risks;
  }

  private generateRiskRecommendations(
    riskMetrics: RiskMetrics,
    diversificationScore: number,
    userVector: any
  ): string[] {
    const recommendations: string[] = [];
    
    // Sharpe ratio recommendations
    if (riskMetrics.sharpeRatio < 0.5) {
      recommendations.push('Consider improving risk-adjusted returns through better asset selection');
    }
    
    // Diversification recommendations
    if (diversificationScore < 0.6) {
      recommendations.push('Increase portfolio diversification to reduce concentration risk');
    }
    
    // Volatility recommendations based on user risk tolerance
    const userRiskTolerance = userVector.riskProfile?.tolerance || 5;
    const volatilityThreshold = userRiskTolerance * 0.02; // 2% per risk level
    
    if (riskMetrics.volatility > volatilityThreshold) {
      recommendations.push('Portfolio volatility exceeds your risk tolerance - consider reducing equity allocation');
    }
    
    // Beta recommendations
    if (Math.abs(riskMetrics.beta - 1) > 0.3) {
      if (riskMetrics.beta > 1.3) {
        recommendations.push('High portfolio beta increases market sensitivity - consider defensive positions');
      } else if (riskMetrics.beta < 0.7) {
        recommendations.push('Low portfolio beta may limit growth potential - consider growth positions');
      }
    }
    
    return recommendations;
  }

  private generateRiskAlerts(riskMetrics: RiskMetrics, userVector: any): Array<{
    severity: 'low' | 'medium' | 'high';
    message: string;
    action: string;
  }> {
    const alerts = [];
    
    // High volatility alert
    if (riskMetrics.volatility > 0.25) {
      alerts.push({
        severity: 'high' as const,
        message: 'Portfolio volatility exceeds 25% - significant risk exposure',
        action: 'Review and reduce high-risk positions'
      });
    }
    
    // Poor risk-adjusted returns
    if (riskMetrics.sharpeRatio < 0.3) {
      alerts.push({
        severity: 'medium' as const,
        message: 'Low Sharpe ratio indicates poor risk-adjusted performance',
        action: 'Optimize portfolio for better risk-return balance'
      });
    }
    
    // High maximum drawdown
    if (riskMetrics.maxDrawdown > 0.30) {
      alerts.push({
        severity: 'medium' as const,
        message: 'Potential maximum drawdown exceeds 30%',
        action: 'Consider adding defensive assets or reducing position sizes'
      });
    }
    
    return alerts;
  }

  private calculateOverallRiskScore(
    riskMetrics: RiskMetrics,
    diversificationScore: number,
    userVector: any
  ): number {
    // Combine multiple risk factors into a single score (1-10 scale)
    let score = 5; // Start with neutral
    
    // Adjust for volatility
    score += (riskMetrics.volatility - 0.15) * 10; // Baseline 15% volatility
    
    // Adjust for diversification
    score -= (diversificationScore - 0.5) * 4;
    
    // Adjust for Sharpe ratio
    score -= (riskMetrics.sharpeRatio - 0.8) * 2;
    
    // Clamp to 1-10 range
    return Math.max(1, Math.min(10, score));
  }

  async getPersonalizedRiskGuidance(userId: string, riskAssessment: RiskAssessment): Promise<string[]> {
    const userVector = await memorySystem.getUserVector(userId);
    const guidance: string[] = [];
    
    // Personalize based on user's learning level
    const conceptMastery = userVector.conceptMastery || {};
    
    if (conceptMastery['diversification'] < 0.7) {
      guidance.push('Focus on learning diversification principles to better understand your portfolio risk');
    }
    
    if (conceptMastery['var'] < 0.5 && riskAssessment.overallRiskScore > 7) {
      guidance.push('Consider learning about Value at Risk (VaR) to better quantify your portfolio risk');
    }
    
    // Risk tolerance alignment
    const userRiskTolerance = userVector.riskProfile?.tolerance || 5;
    if (riskAssessment.overallRiskScore > userRiskTolerance + 2) {
      guidance.push('Your portfolio risk exceeds your stated risk tolerance - consider rebalancing');
    }
    
    return guidance;
  }
}

export const riskAnalysisService = new RiskAnalysisService();