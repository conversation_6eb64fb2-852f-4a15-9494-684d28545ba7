// Gemini API integration utilities
// Note: Replace with your actual Gemini API key

export interface GeminiConfig {
  apiKey: string;
  model: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface GeminiResponse {
  content: string;
  suggestions?: string[];
}

class GeminiClient {
  private apiKey: string;
  private model: string;
  private baseUrl: string;

  constructor(config: GeminiConfig) {
    this.apiKey = config.apiKey;
    this.model = config.model || 'gemini-pro';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  }

  async generateContent(
    messages: ChatMessage[],
    context?: {
      userBehavior?: any;
      financialProfile?: any;
      learningHistory?: any;
    }
  ): Promise<GeminiResponse> {
    try {
      // Construct the system prompt based on context
      const systemPrompt = this.buildSystemPrompt(context);
      
      // Format messages for Gemini API
      const formattedMessages = this.formatMessages(messages, systemPrompt);

      const response = await fetch(
        `${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: formattedMessages,
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 1024,
            },
            safetySettings: [
              {
                category: 'HARM_CATEGORY_HARASSMENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_HATE_SPEECH',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
      
      return {
        content,
        suggestions: this.generateSuggestions(content, messages),
      };
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private buildSystemPrompt(context?: any): string {
    let prompt = `You are an expert AI Financial Strategist and Personal Finance Advisor. You ONLY respond to financial and money-related questions. Your expertise covers:

CORE AREAS:
- Personal budgeting and expense management
- Investment strategies (stocks, bonds, ETFs, mutual funds, real estate)
- Retirement planning (401k, IRA, pension strategies)
- Debt management and credit improvement
- Tax planning and optimization
- Insurance and risk management
- Emergency fund planning
- Financial goal setting and achievement

RESPONSE STYLE:
1. Provide detailed, actionable advice with specific examples
2. Include real-world scenarios and calculations when relevant
3. Explain complex concepts in simple, understandable terms
4. Always include practical next steps the user can take
5. Offer multiple perspectives or strategies when appropriate
6. Use analogies to make financial concepts relatable

IMPORTANT RULES:
- ONLY answer questions related to finance, money, investing, budgeting, or economic topics
- If asked about non-financial topics, politely redirect to financial matters
- Always prioritize the user's financial safety and long-term wealth building
- Provide disclaimers for investment advice (not personalized financial advice)
- Include relevant examples, calculations, or scenarios in your responses
- End responses with 2-3 related follow-up questions or suggestions

RESPONSE FORMAT:
- Start with a clear, direct answer
- Provide detailed explanation with examples
- Include actionable steps or recommendations
- Conclude with related suggestions for further exploration`;

    if (context?.userBehavior) {
      prompt += `\n\nUser Behavior Context: ${JSON.stringify(context.userBehavior)}`;
    }

    if (context?.financialProfile) {
      prompt += `\n\nUser Financial Profile: ${JSON.stringify(context.financialProfile)}`;
    }

    if (context?.learningHistory) {
      prompt += `\n\nUser Learning History: ${JSON.stringify(context.learningHistory)}`;
    }

    return prompt;
  }

  private formatMessages(messages: ChatMessage[], systemPrompt: string) {
    const formatted = [];
    
    // Add system prompt as first user message
    formatted.push({
      role: 'user',
      parts: [{ text: systemPrompt }],
    });

    // Add conversation history
    messages.forEach((message) => {
      formatted.push({
        role: message.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: message.content }],
      });
    });

    return formatted;
  }

  private generateSuggestions(content: string, messages: ChatMessage[]): string[] {
    // Generate contextual follow-up suggestions based on the conversation
    const lastUserMessage = messages.filter(m => m.role === 'user').pop()?.content || '';
    
    const suggestions = [];
    
    if (lastUserMessage.toLowerCase().includes('budget')) {
      suggestions.push(
        'How do I track my expenses effectively?',
        'What are the best budgeting apps?',
        'Help me set realistic savings goals'
      );
    } else if (lastUserMessage.toLowerCase().includes('invest')) {
      suggestions.push(
        'What\'s the difference between stocks and bonds?',
        'How much should I invest monthly?',
        'Explain diversification strategies'
      );
    } else if (lastUserMessage.toLowerCase().includes('retirement')) {
      suggestions.push(
        'Calculate my retirement savings needs',
        'Explain 401(k) vs IRA differences',
        'How to maximize employer matching'
      );
    } else {
      suggestions.push(
        'Tell me about emergency funds',
        'How do I improve my credit score?',
        'Explain compound interest'
      );
    }
    
    return suggestions.slice(0, 3);
  }
}

// Export singleton instance
export const geminiClient = new GeminiClient({
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || '',
  model: 'gemini-pro',
});

// Utility functions for financial calculations
export const financialCalculations = {
  compoundInterest: (principal: number, rate: number, time: number, compoundingFrequency = 12) => {
    return principal * Math.pow(1 + rate / compoundingFrequency, compoundingFrequency * time);
  },

  monthlyPayment: (principal: number, rate: number, months: number) => {
    const monthlyRate = rate / 12;
    return (principal * monthlyRate * Math.pow(1 + monthlyRate, months)) / 
           (Math.pow(1 + monthlyRate, months) - 1);
  },

  retirementSavings: (currentAge: number, retirementAge: number, currentSavings: number, monthlyContribution: number, expectedReturn: number) => {
    const years = retirementAge - currentAge;
    const futureValueCurrent = financialCalculations.compoundInterest(currentSavings, expectedReturn, years, 1);
    const futureValueContributions = monthlyContribution * 12 * 
      ((Math.pow(1 + expectedReturn, years) - 1) / expectedReturn);
    return futureValueCurrent + futureValueContributions;
  },

  riskTolerance: (age: number, investmentHorizon: number, riskCapacity: number) => {
    // Simplified risk tolerance calculation
    const ageFactor = (100 - age) / 100;
    const timeFactor = Math.min(investmentHorizon / 30, 1);
    const capacityFactor = riskCapacity / 10;
    return (ageFactor + timeFactor + capacityFactor) / 3;
  }
};