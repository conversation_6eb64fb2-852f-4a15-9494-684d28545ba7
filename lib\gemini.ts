// Gemini API integration utilities
// Note: Replace with your actual Gemini API key

export interface GeminiConfig {
  apiKey: string;
  model: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface GeminiResponse {
  content: string;
  suggestions?: string[];
}

class GeminiClient {
  private apiKey: string;
  private model: string;
  private baseUrl: string;

  constructor(config: GeminiConfig) {
    this.apiKey = config.apiKey;
    this.model = config.model || 'gemini-2.0-flash';
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  }

  async generateContent(
    messages: ChatMessage[],
    context?: {
      userBehavior?: any;
      financialProfile?: any;
      learningHistory?: any;
    }
  ): Promise<GeminiResponse> {
    try {
      // Construct the system prompt based on context
      const systemPrompt = this.buildSystemPrompt(context);
      
      // Format messages for Gemini API
      const formattedMessages = this.formatMessages(messages, systemPrompt);

      const response = await fetch(
        `${this.baseUrl}/models/${this.model}:generateContent`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-goog-api-key': this.apiKey,
          },
          body: JSON.stringify({
            contents: formattedMessages,
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 2048,
            },
            safetySettings: [
              {
                category: 'HARM_CATEGORY_HARASSMENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_HATE_SPEECH',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
              {
                category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE',
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
      
      return {
        content,
        suggestions: this.generateSuggestions(content, messages),
      };
    } catch (error) {
      console.error('Gemini API error:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  private buildSystemPrompt(context?: any): string {
    let prompt = `You are an expert AI Financial Strategist and Personal Finance Advisor. You ONLY respond to financial and money-related questions. Your expertise covers all aspects of personal finance, investing, and economic planning.

CORE AREAS:
- Personal budgeting and expense management
- Investment strategies (stocks, bonds, ETFs, mutual funds, real estate)
- Retirement planning (401k, IRA, pension strategies)
- Debt management and credit improvement
- Tax planning and optimization
- Insurance and risk management
- Emergency fund planning
- Financial goal setting and achievement

CRITICAL RESPONSE STRUCTURE - Use this EXACT format for ALL responses:

## 📋 **Quick Answer**
[Provide a 1-2 sentence direct answer to the question]

## 🎯 **Definition & Core Concept**
[Explain the financial concept clearly with key terminology]

## 💡 **Practical Example**
[Provide a specific, realistic example with actual numbers and scenarios]

## 🌍 **Real-World Application**
[Show how this applies in real life with concrete use cases]

## 📈 **Market Impact & Considerations**
[Explain how this affects markets, economy, or personal financial position]

## ✅ **Action Steps**
1. [Specific actionable step]
2. [Another specific actionable step]
3. [Third specific actionable step]

## 🔍 **Key Considerations**
- [Important factor to consider]
- [Risk or limitation to be aware of]
- [Additional insight or tip]

## 📊 **Example Calculation** (when applicable)
[Include specific numbers, formulas, or scenarios]

FORMATTING RULES:
- Use markdown formatting with headers, bold text, lists, and emojis
- Include specific dollar amounts, percentages, and timeframes
- Use tables for comparisons when helpful
- Bold important terms and concepts
- Use bullet points for lists and action items

IMPORTANT RULES:
- ONLY answer financial questions - redirect non-financial topics politely
- Always include disclaimers for investment advice
- Provide realistic examples with actual numbers
- Focus on long-term wealth building and financial safety
- Include both benefits and risks in your explanations`;

    if (context?.userBehavior) {
      prompt += `\n\nUser Behavior Context: ${JSON.stringify(context.userBehavior)}`;
    }

    if (context?.financialProfile) {
      prompt += `\n\nUser Financial Profile: ${JSON.stringify(context.financialProfile)}`;
    }

    if (context?.learningHistory) {
      prompt += `\n\nUser Learning History: ${JSON.stringify(context.learningHistory)}`;
    }

    return prompt;
  }

  private formatMessages(messages: ChatMessage[], systemPrompt: string) {
    const formatted = [];
    
    // Add system prompt as first user message
    formatted.push({
      role: 'user',
      parts: [{ text: systemPrompt }],
    });

    // Add conversation history
    messages.forEach((message) => {
      formatted.push({
        role: message.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: message.content }],
      });
    });

    return formatted;
  }

  private generateSuggestions(content: string, messages: ChatMessage[]): string[] {
    // Generate contextual follow-up suggestions based on the conversation
    const lastUserMessage = messages.filter(m => m.role === 'user').pop()?.content || '';
    
    const suggestions = [];
    
    if (lastUserMessage.toLowerCase().includes('budget')) {
      suggestions.push(
        'How do I track my expenses effectively?',
        'What are the best budgeting apps?',
        'Help me set realistic savings goals'
      );
    } else if (lastUserMessage.toLowerCase().includes('invest')) {
      suggestions.push(
        'What\'s the difference between stocks and bonds?',
        'How much should I invest monthly?',
        'Explain diversification strategies'
      );
    } else if (lastUserMessage.toLowerCase().includes('retirement')) {
      suggestions.push(
        'Calculate my retirement savings needs',
        'Explain 401(k) vs IRA differences',
        'How to maximize employer matching'
      );
    } else {
      suggestions.push(
        'Tell me about emergency funds',
        'How do I improve my credit score?',
        'Explain compound interest'
      );
    }
    
    return suggestions.slice(0, 3);
  }
}

// Export singleton instance
export const geminiClient = new GeminiClient({
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || '',
  model: 'gemini-2.0-flash',
});

// Utility functions for financial calculations
export const financialCalculations = {
  compoundInterest: (principal: number, rate: number, time: number, compoundingFrequency = 12) => {
    return principal * Math.pow(1 + rate / compoundingFrequency, compoundingFrequency * time);
  },

  monthlyPayment: (principal: number, rate: number, months: number) => {
    const monthlyRate = rate / 12;
    return (principal * monthlyRate * Math.pow(1 + monthlyRate, months)) / 
           (Math.pow(1 + monthlyRate, months) - 1);
  },

  retirementSavings: (currentAge: number, retirementAge: number, currentSavings: number, monthlyContribution: number, expectedReturn: number) => {
    const years = retirementAge - currentAge;
    const futureValueCurrent = financialCalculations.compoundInterest(currentSavings, expectedReturn, years, 1);
    const futureValueContributions = monthlyContribution * 12 * 
      ((Math.pow(1 + expectedReturn, years) - 1) / expectedReturn);
    return futureValueCurrent + futureValueContributions;
  },

  riskTolerance: (age: number, investmentHorizon: number, riskCapacity: number) => {
    // Simplified risk tolerance calculation
    const ageFactor = (100 - age) / 100;
    const timeFactor = Math.min(investmentHorizon / 30, 1);
    const capacityFactor = riskCapacity / 10;
    return (ageFactor + timeFactor + capacityFactor) / 3;
  }
};