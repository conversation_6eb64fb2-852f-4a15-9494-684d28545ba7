// Dynamic Quote and Insights Engine
import { cacheService } from './cache-service';
import { memorySystem } from './memory-system';

export interface MarketQuote {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  timestamp: Date;
}

export interface FinancialInsight {
  id: string;
  title: string;
  content: string;
  category: 'market' | 'educational' | 'personal' | 'strategy';
  relevanceScore: number;
  concepts: string[];
  actionable: boolean;
  timestamp: Date;
  personalizedFor?: string;
}

export interface DailyQuote {
  id: string;
  quote: string;
  author: string;
  category: string;
  explanation: string;
  relatedConcepts: string[];
  practicalApplication: string;
}

export class QuoteEngine {
  private marketData: Map<string, MarketQuote> = new Map();
  private insightTemplates: FinancialInsight[] = [];
  private dailyQuotes: DailyQuote[] = [];

  constructor() {
    this.initializeInsightTemplates();
    this.initializeDailyQuotes();
    this.startMarketDataUpdates();
  }

  private initializeInsightTemplates() {
    this.insightTemplates = [
      {
        id: 'rebalancing_opportunity',
        title: 'Portfolio Rebalancing Alert',
        content: 'Your equity allocation has drifted {drift_percentage}% from target. Consider rebalancing to maintain your desired risk profile.',
        category: 'personal',
        relevanceScore: 0.8,
        concepts: ['asset_allocation', 'rebalancing', 'risk_management'],
        actionable: true,
        timestamp: new Date(),
      },
      {
        id: 'volatility_education',
        title: 'Understanding Market Volatility',
        content: 'Recent market movements show increased volatility. This is normal and expected - volatility creates opportunities for long-term investors.',
        category: 'educational',
        relevanceScore: 0.7,
        concepts: ['volatility', 'market_cycles', 'long_term_investing'],
        actionable: false,
        timestamp: new Date(),
      },
      {
        id: 'diversification_reminder',
        title: 'Diversification Check',
        content: 'Your portfolio shows concentration in {sector}. Consider diversifying across sectors to reduce specific risk.',
        category: 'strategy',
        relevanceScore: 0.9,
        concepts: ['diversification', 'sector_allocation', 'specific_risk'],
        actionable: true,
        timestamp: new Date(),
      }
    ];
  }

  private initializeDailyQuotes() {
    this.dailyQuotes = [
      {
        id: 'buffett_time',
        quote: 'Time is the friend of the wonderful business, the enemy of the mediocre.',
        author: 'Warren Buffett',
        category: 'investing',
        explanation: 'Quality companies compound wealth over time, while poor businesses deteriorate. This emphasizes the importance of both time horizon and investment selection.',
        relatedConcepts: ['compound_interest', 'quality_investing', 'time_horizon'],
        practicalApplication: 'Focus on high-quality companies and hold them for extended periods rather than frequent trading.'
      },
      {
        id: 'markowitz_diversification',
        quote: 'Diversification is the only free lunch in investing.',
        author: 'Harry Markowitz',
        category: 'risk_management',
        explanation: 'Proper diversification can reduce portfolio risk without sacrificing expected returns - a rare win-win in finance.',
        relatedConcepts: ['diversification', 'efficient_frontier', 'portfolio_theory'],
        practicalApplication: 'Spread investments across uncorrelated assets to optimize your risk-return profile.'
      },
      {
        id: 'bogle_costs',
        quote: 'In investing, you get what you don\'t pay for.',
        author: 'John Bogle',
        category: 'costs',
        explanation: 'Investment costs directly reduce returns. Lower-cost investments typically provide better net returns to investors.',
        relatedConcepts: ['expense_ratios', 'cost_efficiency', 'index_investing'],
        practicalApplication: 'Choose low-cost index funds and ETFs to maximize your investment returns over time.'
      }
    ];
  }

  async getDailyInsights(userId: string): Promise<FinancialInsight[]> {
    const cacheKey = `daily_insights_${userId}_${new Date().toDateString()}`;
    
    // Check cache first
    const cachedInsights = await cacheService.get(cacheKey);
    if (cachedInsights) {
      return cachedInsights;
    }

    // Generate personalized insights
    const userVector = await memorySystem.getUserVector(userId);
    const personalizedInsights = await this.generatePersonalizedInsights(userVector);
    
    // Add market-based insights
    const marketInsights = await this.generateMarketInsights();
    
    // Combine and rank insights
    const allInsights = [...personalizedInsights, ...marketInsights];
    const rankedInsights = this.rankInsightsByRelevance(allInsights, userVector);
    
    // Cache for the day
    await cacheService.set(cacheKey, rankedInsights, 86400); // 24 hours
    
    return rankedInsights.slice(0, 5); // Return top 5 insights
  }

  private async generatePersonalizedInsights(userVector: any): Promise<FinancialInsight[]> {
    const insights: FinancialInsight[] = [];
    
    // Learning progress insights
    const masteredConcepts = Object.entries(userVector.conceptMastery || {})
      .filter(([_, mastery]) => (mastery as number) > 0.8)
      .map(([concept, _]) => concept);
    
    if (masteredConcepts.length > 3) {
      insights.push({
        id: 'learning_milestone',
        title: 'Learning Milestone Achieved',
        content: `Congratulations! You've mastered ${masteredConcepts.length} key financial concepts. Ready to explore advanced strategies?`,
        category: 'personal',
        relevanceScore: 0.9,
        concepts: masteredConcepts,
        actionable: true,
        timestamp: new Date(),
        personalizedFor: userVector.userId
      });
    }
    
    // Risk profile insights
    if (userVector.riskProfile?.tolerance > 8 && userVector.riskProfile?.timeHorizon > 15) {
      insights.push({
        id: 'aggressive_strategy',
        title: 'High-Growth Strategy Opportunity',
        content: 'Your risk profile suggests you could benefit from growth-oriented strategies. Consider increasing equity allocation.',
        category: 'strategy',
        relevanceScore: 0.8,
        concepts: ['asset_allocation', 'growth_investing', 'risk_tolerance'],
        actionable: true,
        timestamp: new Date(),
        personalizedFor: userVector.userId
      });
    }
    
    return insights;
  }

  private async generateMarketInsights(): Promise<FinancialInsight[]> {
    const insights: FinancialInsight[] = [];
    
    // Simulate market-based insights
    const marketVolatility = Math.random() * 0.3 + 0.1; // 10-40% volatility
    
    if (marketVolatility > 0.25) {
      insights.push({
        id: 'high_volatility',
        title: 'Elevated Market Volatility',
        content: `Current market volatility is ${(marketVolatility * 100).toFixed(1)}%. This creates both risks and opportunities for disciplined investors.`,
        category: 'market',
        relevanceScore: 0.7,
        concepts: ['volatility', 'market_timing', 'opportunity_cost'],
        actionable: false,
        timestamp: new Date()
      });
    }
    
    return insights;
  }

  private rankInsightsByRelevance(insights: FinancialInsight[], userVector: any): FinancialInsight[] {
    return insights.sort((a, b) => {
      let scoreA = a.relevanceScore;
      let scoreB = b.relevanceScore;
      
      // Boost score for concepts user is learning
      const userConcepts = Object.keys(userVector.conceptMastery || {});
      const conceptOverlapA = a.concepts.filter(c => userConcepts.includes(c)).length;
      const conceptOverlapB = b.concepts.filter(c => userConcepts.includes(c)).length;
      
      scoreA += conceptOverlapA * 0.1;
      scoreB += conceptOverlapB * 0.1;
      
      // Boost actionable insights for engaged users
      if (userVector.behavioralPatterns?.engagementScore > 0.7) {
        if (a.actionable) scoreA += 0.2;
        if (b.actionable) scoreB += 0.2;
      }
      
      return scoreB - scoreA;
    });
  }

  async getTodaysQuote(userId?: string): Promise<DailyQuote> {
    const today = new Date().toDateString();
    const cacheKey = `daily_quote_${today}`;
    
    // Check cache
    const cachedQuote = await cacheService.get(cacheKey);
    if (cachedQuote) {
      return cachedQuote;
    }
    
    // Select quote based on user preferences or randomly
    let selectedQuote: DailyQuote;
    
    if (userId) {
      const userVector = await memorySystem.getUserVector(userId);
      selectedQuote = this.selectPersonalizedQuote(userVector);
    } else {
      const quoteIndex = Math.floor(Math.random() * this.dailyQuotes.length);
      selectedQuote = this.dailyQuotes[quoteIndex];
    }
    
    // Cache for the day
    await cacheService.set(cacheKey, selectedQuote, 86400);
    
    return selectedQuote;
  }

  private selectPersonalizedQuote(userVector: any): DailyQuote {
    // Score quotes based on user's learning progress and interests
    const scoredQuotes = this.dailyQuotes.map(quote => {
      let score = Math.random(); // Base randomness
      
      // Boost score for relevant concepts
      const conceptOverlap = quote.relatedConcepts.filter(concept =>
        userVector.conceptMastery?.[concept] > 0.3
      ).length;
      
      score += conceptOverlap * 0.3;
      
      // Boost based on category preferences
      if (userVector.learningStyle?.topicPreferences?.includes(quote.category)) {
        score += 0.5;
      }
      
      return { quote, score };
    });
    
    // Return highest scoring quote
    return scoredQuotes.sort((a, b) => b.score - a.score)[0].quote;
  }

  private startMarketDataUpdates() {
    // Simulate real-time market data updates
    setInterval(() => {
      this.updateMarketData();
    }, 30000); // Update every 30 seconds
  }

  private updateMarketData() {
    const symbols = ['SPY', 'QQQ', 'IWM', 'GLD', 'TLT'];
    
    symbols.forEach(symbol => {
      const lastPrice = this.marketData.get(symbol)?.price || 100;
      const change = (Math.random() - 0.5) * 2; // -1 to +1
      const newPrice = lastPrice + change;
      
      this.marketData.set(symbol, {
        symbol,
        price: newPrice,
        change,
        changePercent: (change / lastPrice) * 100,
        volume: Math.floor(Math.random() * 1000000),
        timestamp: new Date()
      });
    });
  }

  async getMarketData(): Promise<MarketQuote[]> {
    return Array.from(this.marketData.values());
  }

  async generateContextualInsight(concepts: string[], userVector: any): Promise<FinancialInsight | null> {
    // Generate insights based on current conversation context
    const relevantTemplates = this.insightTemplates.filter(template =>
      template.concepts.some(concept => concepts.includes(concept))
    );
    
    if (relevantTemplates.length === 0) return null;
    
    const template = relevantTemplates[0];
    
    return {
      ...template,
      id: `contextual_${Date.now()}`,
      timestamp: new Date(),
      personalizedFor: userVector.userId,
      relevanceScore: 0.9 // High relevance for contextual insights
    };
  }
}

export const quoteEngine = new QuoteEngine();