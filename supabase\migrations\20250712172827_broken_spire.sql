/*
  # Intelligent Finance Coach Database Schema

  1. New Tables
    - `user_vectors` - Stores user learning vectors and behavioral patterns
    - `concept_nodes` - Financial concepts with relationships and metadata
    - `user_sessions` - User interaction sessions with analytics
    - `chat_history` - Conversation history with context and engagement metrics
    - `user_achievements` - Learning achievements and milestones
    - `concept_relationships` - Explicit concept prerequisite and related mappings
    - `learning_insights` - AI-generated personalized learning insights
    - `risk_assessments` - Portfolio risk analysis results
    - `market_data_cache` - Cached market data and quotes

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to access their own data
    - Add policies for public read access to concept nodes

  3. Indexes
    - Add performance indexes for common queries
    - Add composite indexes for analytics queries

  4. Functions
    - Add utility functions for data aggregation
    - Add triggers for automatic timestamp updates
*/

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- User Vectors Table
CREATE TABLE IF NOT EXISTS user_vectors (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id text NOT NULL UNIQUE,
  concept_mastery jsonb DEFAULT '{}',
  risk_profile jsonb DEFAULT '{}',
  learning_style jsonb DEFAULT '{}',
  behavioral_patterns jsonb DEFAULT '{}',
  concept_connections jsonb DEFAULT '{}',
  progress_history jsonb DEFAULT '[]',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Concept Nodes Table
CREATE TABLE IF NOT EXISTS concept_nodes (
  id text PRIMARY KEY,
  name text NOT NULL,
  description text NOT NULL,
  difficulty integer NOT NULL CHECK (difficulty >= 1 AND difficulty <= 10),
  prerequisites jsonb DEFAULT '[]',
  related_concepts jsonb DEFAULT '[]',
  mastery_threshold decimal DEFAULT 0.7 CHECK (mastery_threshold >= 0 AND mastery_threshold <= 1),
  examples jsonb DEFAULT '[]',
  practical_applications jsonb DEFAULT '[]',
  category text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- User Sessions Table
CREATE TABLE IF NOT EXISTS user_sessions (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id text NOT NULL,
  session_id text NOT NULL,
  start_time timestamptz NOT NULL,
  end_time timestamptz,
  duration integer, -- in seconds
  interactions jsonb DEFAULT '[]',
  learning_progress jsonb DEFAULT '{}',
  concepts_explored jsonb DEFAULT '[]',
  questions_asked integer DEFAULT 0,
  simulations_run integer DEFAULT 0,
  engagement_score decimal DEFAULT 0.5 CHECK (engagement_score >= 0 AND engagement_score <= 1),
  created_at timestamptz DEFAULT now()
);

-- Chat History Table
CREATE TABLE IF NOT EXISTS chat_history (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id text NOT NULL,
  session_id text NOT NULL,
  role text NOT NULL CHECK (role IN ('user', 'assistant')),
  content text NOT NULL,
  concepts jsonb DEFAULT '[]',
  engagement decimal DEFAULT 0.5 CHECK (engagement >= 0 AND engagement <= 1),
  complexity integer DEFAULT 5 CHECK (complexity >= 1 AND complexity <= 10),
  timestamp timestamptz NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- User Achievements Table
CREATE TABLE IF NOT EXISTS user_achievements (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id text NOT NULL,
  achievement_id text NOT NULL,
  title text NOT NULL,
  description text NOT NULL,
  category text NOT NULL,
  unlocked_at timestamptz NOT NULL,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  UNIQUE(user_id, achievement_id)
);

-- Concept Relationships Table
CREATE TABLE IF NOT EXISTS concept_relationships (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  from_concept text NOT NULL,
  to_concept text NOT NULL,
  relationship_type text NOT NULL CHECK (relationship_type IN ('prerequisite', 'related', 'builds_on')),
  strength decimal DEFAULT 0.5 CHECK (strength >= 0 AND strength <= 1),
  created_at timestamptz DEFAULT now(),
  UNIQUE(from_concept, to_concept, relationship_type)
);

-- Learning Insights Table
CREATE TABLE IF NOT EXISTS learning_insights (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id text NOT NULL,
  insight_type text NOT NULL CHECK (insight_type IN ('strength', 'weakness', 'opportunity', 'recommendation')),
  title text NOT NULL,
  description text NOT NULL,
  confidence decimal NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
  actionable boolean DEFAULT false,
  related_concepts jsonb DEFAULT '[]',
  suggested_actions jsonb DEFAULT '[]',
  generated_at timestamptz NOT NULL,
  expires_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Risk Assessments Table
CREATE TABLE IF NOT EXISTS risk_assessments (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id text NOT NULL,
  portfolio_data jsonb NOT NULL,
  risk_metrics jsonb NOT NULL,
  scenarios jsonb DEFAULT '[]',
  recommendations jsonb DEFAULT '[]',
  overall_risk_score decimal CHECK (overall_risk_score >= 1 AND overall_risk_score <= 10),
  assessment_date timestamptz NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Market Data Cache Table
CREATE TABLE IF NOT EXISTS market_data_cache (
  id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
  data_type text NOT NULL,
  symbol text,
  data jsonb NOT NULL,
  timestamp timestamptz NOT NULL,
  expires_at timestamptz NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE user_vectors ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE concept_nodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE concept_relationships ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_data_cache ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_vectors
CREATE POLICY "Users can read own vector"
  ON user_vectors
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can update own vector"
  ON user_vectors
  FOR ALL
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

-- RLS Policies for user_sessions
CREATE POLICY "Users can read own sessions"
  ON user_sessions
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can insert own sessions"
  ON user_sessions
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can update own sessions"
  ON user_sessions
  FOR UPDATE
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

-- RLS Policies for chat_history
CREATE POLICY "Users can read own chat history"
  ON chat_history
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can insert own chat messages"
  ON chat_history
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt() ->> 'sub');

-- RLS Policies for user_achievements
CREATE POLICY "Users can read own achievements"
  ON user_achievements
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can insert own achievements"
  ON user_achievements
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt() ->> 'sub');

-- RLS Policies for learning_insights
CREATE POLICY "Users can read own insights"
  ON learning_insights
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "System can insert insights"
  ON learning_insights
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- RLS Policies for risk_assessments
CREATE POLICY "Users can read own risk assessments"
  ON risk_assessments
  FOR SELECT
  TO authenticated
  USING (user_id = auth.jwt() ->> 'sub');

CREATE POLICY "Users can insert own risk assessments"
  ON risk_assessments
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.jwt() ->> 'sub');

-- Public read access for concept_nodes and concept_relationships
CREATE POLICY "Anyone can read concept nodes"
  ON concept_nodes
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Anyone can read concept relationships"
  ON concept_relationships
  FOR SELECT
  TO authenticated
  USING (true);

-- Public read access for market data cache (with expiration check)
CREATE POLICY "Anyone can read non-expired market data"
  ON market_data_cache
  FOR SELECT
  TO authenticated
  USING (expires_at > now());

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_vectors_user_id ON user_vectors(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_user_session ON chat_history(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_chat_history_timestamp ON chat_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_insights_user_id ON learning_insights(user_id);
CREATE INDEX IF NOT EXISTS idx_learning_insights_expires ON learning_insights(expires_at);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_user_id ON risk_assessments(user_id);
CREATE INDEX IF NOT EXISTS idx_market_data_expires ON market_data_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_market_data_symbol ON market_data_cache(symbol);

-- GIN indexes for JSONB columns
CREATE INDEX IF NOT EXISTS idx_user_vectors_concept_mastery ON user_vectors USING GIN(concept_mastery);
CREATE INDEX IF NOT EXISTS idx_concept_nodes_prerequisites ON concept_nodes USING GIN(prerequisites);
CREATE INDEX IF NOT EXISTS idx_concept_nodes_related ON concept_nodes USING GIN(related_concepts);
CREATE INDEX IF NOT EXISTS idx_chat_history_concepts ON chat_history USING GIN(concepts);

-- Text search indexes
CREATE INDEX IF NOT EXISTS idx_concept_nodes_name_search ON concept_nodes USING GIN(name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_concept_nodes_description_search ON concept_nodes USING GIN(description gin_trgm_ops);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_user_vectors_updated_at
  BEFORE UPDATE ON user_vectors
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_concept_nodes_updated_at
  BEFORE UPDATE ON concept_nodes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate user learning velocity
CREATE OR REPLACE FUNCTION calculate_learning_velocity(p_user_id text, p_days integer DEFAULT 7)
RETURNS decimal AS $$
DECLARE
  concept_count integer;
  session_count integer;
  velocity decimal;
BEGIN
  -- Count unique concepts explored in the last p_days
  SELECT COUNT(DISTINCT concept)
  INTO concept_count
  FROM (
    SELECT jsonb_array_elements_text(concepts_explored) as concept
    FROM user_sessions
    WHERE user_id = p_user_id
    AND start_time >= now() - interval '1 day' * p_days
  ) concepts;
  
  -- Count sessions in the same period
  SELECT COUNT(*)
  INTO session_count
  FROM user_sessions
  WHERE user_id = p_user_id
  AND start_time >= now() - interval '1 day' * p_days;
  
  -- Calculate velocity (concepts per session)
  IF session_count > 0 THEN
    velocity := concept_count::decimal / session_count::decimal;
  ELSE
    velocity := 0;
  END IF;
  
  RETURN velocity;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's concept mastery summary
CREATE OR REPLACE FUNCTION get_concept_mastery_summary(p_user_id text)
RETURNS TABLE(
  total_concepts integer,
  mastered_concepts integer,
  average_mastery decimal,
  strongest_category text,
  weakest_concept text
) AS $$
BEGIN
  RETURN QUERY
  WITH mastery_data AS (
    SELECT 
      jsonb_object_keys(concept_mastery) as concept,
      (concept_mastery ->> jsonb_object_keys(concept_mastery))::decimal as mastery
    FROM user_vectors
    WHERE user_id = p_user_id
  ),
  concept_categories AS (
    SELECT 
      md.concept,
      md.mastery,
      cn.category
    FROM mastery_data md
    LEFT JOIN concept_nodes cn ON cn.id = md.concept
  )
  SELECT 
    COUNT(*)::integer as total_concepts,
    COUNT(CASE WHEN mastery >= 0.8 THEN 1 END)::integer as mastered_concepts,
    AVG(mastery) as average_mastery,
    (SELECT category FROM concept_categories WHERE category IS NOT NULL GROUP BY category ORDER BY AVG(mastery) DESC LIMIT 1) as strongest_category,
    (SELECT concept FROM concept_categories ORDER BY mastery ASC LIMIT 1) as weakest_concept
  FROM concept_categories;
END;
$$ LANGUAGE plpgsql;

-- Insert initial concept nodes
INSERT INTO concept_nodes (id, name, description, difficulty, prerequisites, related_concepts, category, examples, practical_applications) VALUES
('diversification', 'Diversification', 'The practice of spreading investments across various assets to reduce risk', 2, '[]', '["correlation", "asset_allocation"]', 'investing', '["Geographic diversification", "Sector diversification", "Asset class diversification"]', '["Portfolio construction", "Risk reduction", "Return optimization"]'),
('asset_allocation', 'Asset Allocation', 'Strategic distribution of investments across different asset classes', 3, '["diversification", "risk_return"]', '["portfolio_theory", "rebalancing", "strategic_allocation"]', 'investing', '["60/40 stocks/bonds", "Target-date funds", "Age-based allocation"]', '["Portfolio construction", "Risk management", "Retirement planning"]'),
('sharpe_ratio', 'Sharpe Ratio', 'Risk-adjusted return measure comparing excess return to volatility', 6, '["risk_return", "standard_deviation", "risk_free_rate"]', '["sortino_ratio", "information_ratio", "risk_metrics"]', 'analysis', '["Portfolio comparison", "Fund evaluation", "Strategy assessment"]', '["Investment selection", "Performance evaluation", "Risk assessment"]'),
('var', 'Value at Risk (VaR)', 'Statistical measure of potential portfolio losses over a specific time period', 8, '["probability", "normal_distribution", "portfolio_theory"]', '["conditional_var", "expected_shortfall", "stress_testing"]', 'risk', '["1% daily VaR", "Portfolio risk limits", "Regulatory capital"]', '["Risk management", "Position sizing", "Regulatory compliance"]'),
('convexity', 'Bond Convexity', 'Measure of bond price sensitivity to interest rate changes beyond duration', 9, '["duration", "yield_curve", "bond_pricing"]', '["duration", "yield_sensitivity", "immunization"]', 'analysis', '["Mortgage-backed securities", "Callable bonds", "Portfolio immunization"]', '["Bond portfolio management", "Interest rate hedging", "Fixed income analysis"]'),
('portfolio_theory', 'Modern Portfolio Theory', 'Framework for constructing portfolios to maximize expected return for a given level of risk', 5, '["diversification", "correlation", "efficient_frontier"]', '["asset_allocation", "sharpe_ratio", "optimization"]', 'investing', '["Efficient frontier", "Capital allocation line", "Optimal portfolios"]', '["Portfolio optimization", "Asset allocation", "Risk budgeting"]'),
('risk_return', 'Risk-Return Relationship', 'Fundamental principle that higher returns require accepting higher risk', 1, '[]', '["diversification", "asset_allocation", "sharpe_ratio"]', 'investing', '["Stocks vs bonds", "High-yield vs investment grade", "Growth vs value"]', '["Investment selection", "Portfolio construction", "Risk assessment"]'),
('correlation', 'Correlation', 'Statistical measure of how two assets move in relation to each other', 3, '["statistics", "covariance"]', '["diversification", "portfolio_theory", "beta"]', 'analysis', '["Stock correlations", "Asset class correlations", "International diversification"]', '["Portfolio construction", "Risk management", "Hedging strategies"]');

-- Insert initial concept relationships
INSERT INTO concept_relationships (from_concept, to_concept, relationship_type, strength) VALUES
('risk_return', 'diversification', 'prerequisite', 0.8),
('diversification', 'asset_allocation', 'prerequisite', 0.9),
('correlation', 'diversification', 'prerequisite', 0.7),
('asset_allocation', 'portfolio_theory', 'prerequisite', 0.8),
('portfolio_theory', 'sharpe_ratio', 'prerequisite', 0.7),
('sharpe_ratio', 'var', 'related', 0.6),
('portfolio_theory', 'convexity', 'related', 0.4),
('diversification', 'portfolio_theory', 'related', 0.5),
('risk_return', 'sharpe_ratio', 'builds_on', 0.6),
('asset_allocation', 'var', 'related', 0.5);