'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { cn } from '@/lib/utils';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({ content, className }: MarkdownRendererProps) {
  return (
    <div className={cn('prose prose-invert prose-sm max-w-none break-words overflow-hidden chat-message', className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
        // Headings
        h1: ({ children }) => (
          <h1 className="text-lg font-bold text-blue-300 mb-3 mt-4 first:mt-0 break-words">
            {children}
          </h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-base font-semibold text-blue-300 mb-2 mt-3 first:mt-0 break-words">
            {children}
          </h2>
        ),
        h3: ({ children }) => (
          <h3 className="text-sm font-semibold text-blue-300 mb-2 mt-3 first:mt-0 break-words">
            {children}
          </h3>
        ),
        
        // Paragraphs
        p: ({ children }) => (
          <p className="text-slate-200 mb-3 leading-relaxed last:mb-0 break-words">
            {children}
          </p>
        ),

        // Lists
        ul: ({ children }) => (
          <ul className="list-disc list-inside text-slate-200 mb-3 space-y-1 pl-4">
            {children}
          </ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal list-inside text-slate-200 mb-3 space-y-1 pl-4">
            {children}
          </ol>
        ),
        li: ({ children }) => (
          <li className="text-slate-200 leading-relaxed break-words ml-2">
            {children}
          </li>
        ),
        
        // Emphasis
        strong: ({ children }) => (
          <strong className="font-semibold text-white">
            {children}
          </strong>
        ),
        em: ({ children }) => (
          <em className="italic text-blue-200">
            {children}
          </em>
        ),
        
        // Code
        code: ({ children, ...props }) => {
          // Check if it's inline code (no language specified)
          const isInline = !props.className || !props.className.startsWith('language-');
          if (isInline) {
            return (
              <code className="bg-slate-600 text-blue-200 px-1.5 py-0.5 rounded text-xs font-mono break-all">
                {children}
              </code>
            );
          }
          return (
            <code className="block bg-slate-600 text-blue-200 p-3 rounded-lg text-xs font-mono overflow-x-auto mb-3 whitespace-pre-wrap break-all">
              {children}
            </code>
          );
        },
        
        // Blockquotes
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-blue-500 pl-4 py-2 bg-slate-600/30 rounded-r-lg mb-3 italic text-slate-300">
            {children}
          </blockquote>
        ),
        
        // Tables
        table: ({ children }) => (
          <div className="overflow-x-auto mb-4 rounded-lg border border-slate-600">
            <table className="min-w-full">
              {children}
            </table>
          </div>
        ),
        thead: ({ children }) => (
          <thead className="bg-slate-600">
            {children}
          </thead>
        ),
        tbody: ({ children }) => (
          <tbody className="bg-slate-700/30">
            {children}
          </tbody>
        ),
        tr: ({ children }) => (
          <tr className="border-b border-slate-600 last:border-b-0">
            {children}
          </tr>
        ),
        th: ({ children }) => (
          <th className="px-4 py-3 text-left text-xs font-semibold text-blue-300 border-r border-slate-600 last:border-r-0 break-words">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="px-4 py-3 text-xs text-slate-200 border-r border-slate-600 last:border-r-0 break-words">
            {children}
          </td>
        ),
        
        // Horizontal rule
        hr: () => (
          <hr className="border-slate-600 my-4" />
        ),
        
        // Links
        a: ({ children, href }) => (
          <a 
            href={href} 
            className="text-blue-400 hover:text-blue-300 underline"
            target="_blank"
            rel="noopener noreferrer"
          >
            {children}
          </a>
        ),
      }}
    >
      {content}
    </ReactMarkdown>
    </div>
  );
}
