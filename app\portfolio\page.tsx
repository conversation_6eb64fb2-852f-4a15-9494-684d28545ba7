"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  PieChart, 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  AlertTriangle,
  Target,
  Zap
} from 'lucide-react';
import { LineChart, Line, AreaChart, Area, ResponsiveContainer, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart, Cell } from 'recharts';

export default function PortfolioPage() {
  const [investmentAmount, setInvestmentAmount] = useState(10000);
  const [timeHorizon, setTimeHorizon] = useState([10]);
  const [riskTolerance, setRiskTolerance] = useState([5]);
  const [selectedStrategy, setSelectedStrategy] = useState('balanced');

  const portfolioStrategies = {
    conservative: {
      name: 'Conservative',
      allocation: [
        { name: 'Bonds', value: 60, color: '#10B981' },
        { name: 'Stocks', value: 30, color: '#3B82F6' },
        { name: 'Cash', value: 10, color: '#6B7280' }
      ],
      expectedReturn: 5.2,
      volatility: 8.1,
      description: 'Lower risk with steady returns'
    },
    balanced: {
      name: 'Balanced',
      allocation: [
        { name: 'Stocks', value: 60, color: '#3B82F6' },
        { name: 'Bonds', value: 30, color: '#10B981' },
        { name: 'REITs', value: 7, color: '#F59E0B' },
        { name: 'Cash', value: 3, color: '#6B7280' }
      ],
      expectedReturn: 7.8,
      volatility: 12.3,
      description: 'Moderate risk with balanced growth'
    },
    aggressive: {
      name: 'Aggressive',
      allocation: [
        { name: 'Stocks', value: 80, color: '#3B82F6' },
        { name: 'Growth Stocks', value: 10, color: '#8B5CF6' },
        { name: 'REITs', value: 7, color: '#F59E0B' },
        { name: 'Bonds', value: 3, color: '#10B981' }
      ],
      expectedReturn: 10.2,
      volatility: 18.7,
      description: 'Higher risk with maximum growth potential'
    }
  };

  const simulationData = [
    { year: 0, conservative: 10000, balanced: 10000, aggressive: 10000 },
    { year: 1, conservative: 10520, balanced: 10780, aggressive: 11020 },
    { year: 2, conservative: 11067, balanced: 11626, aggressive: 12162 },
    { year: 3, conservative: 11643, balanced: 12541, aggressive: 13407 },
    { year: 4, conservative: 12248, balanced: 13527, aggressive: 14786 },
    { year: 5, conservative: 12885, balanced: 14592, aggressive: 16312 },
    { year: 10, conservative: 16436, balanced: 21589, aggressive: 26609 },
    { year: 15, conservative: 20956, balanced: 31943, aggressive: 43420 },
    { year: 20, conservative: 26724, balanced: 47253, aggressive: 70882 }
  ];

  const riskMetrics = [
    {
      metric: 'Maximum Drawdown',
      conservative: '-12%',
      balanced: '-18%',
      aggressive: '-28%',
      description: 'Worst historical decline'
    },
    {
      metric: 'Sharpe Ratio',
      conservative: '0.64',
      balanced: '0.63',
      aggressive: '0.55',
      description: 'Risk-adjusted returns'
    },
    {
      metric: 'Volatility',
      conservative: '8.1%',
      balanced: '12.3%',
      aggressive: '18.7%',
      description: 'Annual price fluctuation'
    }
  ];

  const currentStrategy = portfolioStrategies[selectedStrategy];
  const projectedValue = investmentAmount * Math.pow(1 + currentStrategy.expectedReturn / 100, timeHorizon[0]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Portfolio Simulation</h1>
            <p className="text-slate-300">Test investment strategies with advanced modeling and risk analysis</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {/* Configuration Panel */}
            <div className="lg:col-span-1 space-y-6">
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Target className="h-5 w-5 text-blue-400" />
                    Simulation Parameters
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label className="text-slate-300">Initial Investment</Label>
                    <Input
                      type="number"
                      value={investmentAmount}
                      onChange={(e) => setInvestmentAmount(Number(e.target.value))}
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300">Time Horizon: {timeHorizon[0]} years</Label>
                    <Slider
                      value={timeHorizon}
                      onValueChange={setTimeHorizon}
                      max={30}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300">Risk Tolerance: {riskTolerance[0]}/10</Label>
                    <Slider
                      value={riskTolerance}
                      onValueChange={setRiskTolerance}
                      max={10}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300">Strategy</Label>
                    <Select value={selectedStrategy} onValueChange={setSelectedStrategy}>
                      <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-slate-700 border-slate-600">
                        <SelectItem value="conservative">Conservative</SelectItem>
                        <SelectItem value="balanced">Balanced</SelectItem>
                        <SelectItem value="aggressive">Aggressive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="pt-4 border-t border-slate-700">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-300">Expected Return</span>
                        <span className="text-green-400 font-medium">
                          {currentStrategy.expectedReturn}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Projected Value</span>
                        <span className="text-white font-bold">
                          ${projectedValue.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-300">Total Gain</span>
                        <span className="text-green-400 font-medium">
                          +${(projectedValue - investmentAmount).toLocaleString(undefined, { maximumFractionDigits: 0 })}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Portfolio Allocation */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <PieChart className="h-5 w-5 text-purple-400" />
                    {currentStrategy.name} Allocation
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    {currentStrategy.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={200}>
                    <RechartsPieChart>
                      <Pie
                        dataKey="value"
                        data={currentStrategy.allocation}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={80}
                        fill="#8884d8"
                      >
                        {currentStrategy.allocation.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                  <div className="space-y-2 mt-4">
                    {currentStrategy.allocation.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: item.color }}
                          />
                          <span className="text-sm text-slate-300">{item.name}</span>
                        </div>
                        <span className="text-sm text-white">{item.value}%</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Results */}
            <div className="lg:col-span-2 space-y-6">
              
              {/* Performance Projection */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-400" />
                    Performance Projection
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    Long-term growth simulation across different strategies
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={simulationData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="year" stroke="#9CA3AF" />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          border: '1px solid #374151',
                          borderRadius: '8px'
                        }} 
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="conservative" 
                        stroke="#10B981" 
                        strokeWidth={2}
                        name="Conservative"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="balanced" 
                        stroke="#3B82F6" 
                        strokeWidth={2}
                        name="Balanced"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="aggressive" 
                        stroke="#F59E0B" 
                        strokeWidth={2}
                        name="Aggressive"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Risk Analysis */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" />
                    Risk Analysis
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    Comprehensive risk metrics for informed decision-making
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-slate-700">
                          <th className="text-left py-3 text-slate-300">Metric</th>
                          <th className="text-center py-3 text-green-400">Conservative</th>
                          <th className="text-center py-3 text-blue-400">Balanced</th>
                          <th className="text-center py-3 text-yellow-400">Aggressive</th>
                        </tr>
                      </thead>
                      <tbody>
                        {riskMetrics.map((row, index) => (
                          <tr key={index} className="border-b border-slate-700/50">
                            <td className="py-3">
                              <div>
                                <div className="text-slate-200 font-medium">{row.metric}</div>
                                <div className="text-xs text-slate-400">{row.description}</div>
                              </div>
                            </td>
                            <td className="text-center py-3 text-slate-200">{row.conservative}</td>
                            <td className="text-center py-3 text-slate-200">{row.balanced}</td>
                            <td className="text-center py-3 text-slate-200">{row.aggressive}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Strategy Recommendations */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-green-500/10 border-green-500/30 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-green-400" />
                      <span className="text-sm font-medium text-green-300">Best for Stability</span>
                    </div>
                    <p className="text-xs text-slate-300">
                      Conservative strategy offers steady growth with minimal volatility, 
                      ideal for risk-averse investors near retirement.
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-blue-500/10 border-blue-500/30 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <BarChart3 className="h-4 w-4 text-blue-400" />
                      <span className="text-sm font-medium text-blue-300">Best Balance</span>
                    </div>
                    <p className="text-xs text-slate-300">
                      Balanced approach provides optimal risk-return tradeoff 
                      for most long-term investors with moderate risk tolerance.
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-yellow-500/10 border-yellow-500/30 backdrop-blur-sm">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Zap className="h-4 w-4 text-yellow-400" />
                      <span className="text-sm font-medium text-yellow-300">Best for Growth</span>
                    </div>
                    <p className="text-xs text-slate-300">
                      Aggressive strategy maximizes growth potential for young investors 
                      with high risk tolerance and long time horizons.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}