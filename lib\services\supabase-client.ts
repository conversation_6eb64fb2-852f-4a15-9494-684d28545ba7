// Supabase Client Configuration
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database schema types
export interface UserVector {
  id: string;
  user_id: string;
  concept_mastery: string; // JSON string
  risk_profile: string; // JSON string
  learning_style: string; // JSON string
  behavioral_patterns: string; // JSON string
  concept_connections: string; // JSON string
  progress_history: string; // JSON string
  created_at: string;
  updated_at: string;
}

export interface ConceptNode {
  id: string;
  name: string;
  description: string;
  difficulty: number;
  prerequisites: string; // JSON array
  related_concepts: string; // JSON array
  mastery_threshold: number;
  examples: string; // JSON array
  practical_applications: string; // JSON array
  category: string;
  created_at: string;
  updated_at: string;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_id: string;
  start_time: string;
  end_time?: string;
  duration?: number;
  interactions: string; // JSON array
  learning_progress: string; // JSON object
  concepts_explored: string; // JSON array
  questions_asked: number;
  simulations_run: number;
  created_at: string;
}

export interface ChatHistory {
  id: string;
  user_id: string;
  session_id: string;
  role: 'user' | 'assistant';
  content: string;
  concepts: string; // JSON array
  engagement: number;
  timestamp: string;
  created_at: string;
}

// Database functions
export const dbOperations = {
  // User Vector operations
  async getUserVector(userId: string): Promise<UserVector | null> {
    const { data, error } = await supabase
      .from('user_vectors')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching user vector:', error);
      return null;
    }
    
    return data;
  },

  async upsertUserVector(userVector: Partial<UserVector>): Promise<boolean> {
    const { error } = await supabase
      .from('user_vectors')
      .upsert(userVector);
    
    if (error) {
      console.error('Error upserting user vector:', error);
      return false;
    }
    
    return true;
  },

  // Session operations
  async createSession(session: Partial<UserSession>): Promise<string | null> {
    const { data, error } = await supabase
      .from('user_sessions')
      .insert(session)
      .select('id')
      .single();
    
    if (error) {
      console.error('Error creating session:', error);
      return null;
    }
    
    return data.id;
  },

  async updateSession(sessionId: string, updates: Partial<UserSession>): Promise<boolean> {
    const { error } = await supabase
      .from('user_sessions')
      .update(updates)
      .eq('id', sessionId);
    
    if (error) {
      console.error('Error updating session:', error);
      return false;
    }
    
    return true;
  },

  // Chat history operations
  async saveChatMessage(message: Partial<ChatHistory>): Promise<boolean> {
    const { error } = await supabase
      .from('chat_history')
      .insert(message);
    
    if (error) {
      console.error('Error saving chat message:', error);
      return false;
    }
    
    return true;
  },

  async getChatHistory(userId: string, sessionId: string, limit: number = 50): Promise<ChatHistory[]> {
    const { data, error } = await supabase
      .from('chat_history')
      .select('*')
      .eq('user_id', userId)
      .eq('session_id', sessionId)
      .order('timestamp', { ascending: true })
      .limit(limit);
    
    if (error) {
      console.error('Error fetching chat history:', error);
      return [];
    }
    
    return data || [];
  },

  // Concept operations
  async getAllConcepts(): Promise<ConceptNode[]> {
    const { data, error } = await supabase
      .from('concept_nodes')
      .select('*')
      .order('difficulty', { ascending: true });
    
    if (error) {
      console.error('Error fetching concepts:', error);
      return [];
    }
    
    return data || [];
  },

  async getConceptById(conceptId: string): Promise<ConceptNode | null> {
    const { data, error } = await supabase
      .from('concept_nodes')
      .select('*')
      .eq('id', conceptId)
      .single();
    
    if (error) {
      console.error('Error fetching concept:', error);
      return null;
    }
    
    return data;
  },

  // Analytics operations
  async getUserAnalytics(userId: string, days: number = 30): Promise<any> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);
    
    const { data, error } = await supabase
      .from('user_sessions')
      .select('*')
      .eq('user_id', userId)
      .gte('start_time', startDate.toISOString())
      .order('start_time', { ascending: false });
    
    if (error) {
      console.error('Error fetching user analytics:', error);
      return null;
    }
    
    return data;
  },

  // Real-time subscriptions
  subscribeToUserUpdates(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`user_${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_vectors',
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe();
  },

  subscribeToChatUpdates(sessionId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`chat_${sessionId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'chat_history',
        filter: `session_id=eq.${sessionId}`
      }, callback)
      .subscribe();
  }
};

// Utility functions for data transformation
export const dataTransformers = {
  serializeUserVector(userVector: any): Partial<UserVector> {
    return {
      user_id: userVector.userId,
      concept_mastery: JSON.stringify(userVector.conceptMastery || {}),
      risk_profile: JSON.stringify(userVector.riskProfile || {}),
      learning_style: JSON.stringify(userVector.learningStyle || {}),
      behavioral_patterns: JSON.stringify(userVector.behavioralPatterns || {}),
      concept_connections: JSON.stringify(userVector.conceptConnections || {}),
      progress_history: JSON.stringify(userVector.progressHistory || []),
      updated_at: new Date().toISOString()
    };
  },

  deserializeUserVector(data: UserVector): any {
    return {
      userId: data.user_id,
      conceptMastery: JSON.parse(data.concept_mastery || '{}'),
      riskProfile: JSON.parse(data.risk_profile || '{}'),
      learningStyle: JSON.parse(data.learning_style || '{}'),
      behavioralPatterns: JSON.parse(data.behavioral_patterns || '{}'),
      conceptConnections: JSON.parse(data.concept_connections || '{}'),
      progressHistory: JSON.parse(data.progress_history || '[]')
    };
  },

  serializeChatMessage(message: any): Partial<ChatHistory> {
    return {
      user_id: message.userId,
      session_id: message.sessionId,
      role: message.role,
      content: message.content,
      concepts: JSON.stringify(message.concepts || []),
      engagement: message.engagement || 0.5,
      timestamp: message.timestamp || new Date().toISOString()
    };
  },

  deserializeChatMessage(data: ChatHistory): any {
    return {
      id: data.id,
      userId: data.user_id,
      sessionId: data.session_id,
      role: data.role,
      content: data.content,
      concepts: JSON.parse(data.concepts || '[]'),
      engagement: data.engagement,
      timestamp: new Date(data.timestamp)
    };
  }
};