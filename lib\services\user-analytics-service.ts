// User Analytics and Behavioral Intelligence Service
import { memorySystem } from './memory-system';
import { cacheService } from './cache-service';

export interface UserSession {
  sessionId: string;
  userId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  interactions: Array<{
    type: 'chat' | 'voice' | 'simulation' | 'learning' | 'navigation';
    timestamp: Date;
    data: any;
    engagement: number;
  }>;
  learningProgress: Record<string, number>;
  conceptsExplored: string[];
  questionsAsked: number;
  simulationsRun: number;
}

export interface BehavioralPattern {
  userId: string;
  patterns: {
    sessionFrequency: number; // sessions per week
    averageSessionDuration: number; // minutes
    preferredTimeOfDay: number; // hour 0-23
    preferredDayOfWeek: number; // 0-6
    learningVelocity: number; // concepts per session
    questionComplexity: number; // 1-10 average
    engagementTrend: number[]; // last 10 sessions
    topicPreferences: Record<string, number>;
    interactionModes: Record<string, number>; // chat vs voice vs simulation
  };
  predictions: {
    nextSessionTime: Date;
    likelyTopics: string[];
    recommendedComplexity: number;
    churnRisk: number; // 0-1
    learningPotential: number; // 0-1
  };
}

export interface LearningInsight {
  userId: string;
  type: 'strength' | 'weakness' | 'opportunity' | 'recommendation';
  title: string;
  description: string;
  confidence: number; // 0-1
  actionable: boolean;
  relatedConcepts: string[];
  suggestedActions: string[];
}

export class UserAnalyticsService {
  private activeSessions: Map<string, UserSession> = new Map();
  private behavioralPatterns: Map<string, BehavioralPattern> = new Map();

  async startSession(userId: string): Promise<string> {
    const sessionId = `session_${userId}_${Date.now()}`;
    
    const session: UserSession = {
      sessionId,
      userId,
      startTime: new Date(),
      interactions: [],
      learningProgress: {},
      conceptsExplored: [],
      questionsAsked: 0,
      simulationsRun: 0
    };

    this.activeSessions.set(sessionId, session);
    
    // Update user's session frequency
    await this.updateSessionFrequency(userId);
    
    return sessionId;
  }

  async endSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    session.endTime = new Date();
    session.duration = (session.endTime.getTime() - session.startTime.getTime()) / 1000 / 60; // minutes

    // Analyze session for insights
    await this.analyzeSession(session);
    
    // Update behavioral patterns
    await this.updateBehavioralPatterns(session);
    
    // Store session data
    await this.storeSession(session);
    
    this.activeSessions.delete(sessionId);
  }

  async recordInteraction(sessionId: string, interaction: {
    type: 'chat' | 'voice' | 'simulation' | 'learning' | 'navigation';
    data: any;
    concepts?: string[];
    engagement?: number;
  }): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    const fullInteraction = {
      type: interaction.type,
      timestamp: new Date(),
      data: interaction.data,
      engagement: interaction.engagement || 0.5
    };

    session.interactions.push(fullInteraction);

    // Update session metrics
    if (interaction.type === 'chat' && interaction.data.isQuestion) {
      session.questionsAsked++;
    }
    
    if (interaction.type === 'simulation') {
      session.simulationsRun++;
    }

    if (interaction.concepts) {
      session.conceptsExplored.push(...interaction.concepts);
      
      // Update learning progress
      for (const concept of interaction.concepts) {
        session.learningProgress[concept] = (session.learningProgress[concept] || 0) + 0.1;
      }
    }

    // Real-time behavioral analysis
    await this.performRealTimeAnalysis(session);
  }

  private async analyzeSession(session: UserSession): Promise<void> {
    const userVector = await memorySystem.getUserVector(session.userId);
    
    // Calculate session engagement score
    const avgEngagement = session.interactions.reduce((sum, int) => 
      sum + int.engagement, 0) / session.interactions.length;
    
    // Analyze learning velocity
    const uniqueConcepts = new Set(session.conceptsExplored).size;
    const learningVelocity = uniqueConcepts / (session.duration || 1);
    
    // Update user vector with session insights
    await memorySystem.recordInteraction(session.userId, {
      type: 'session_summary',
      content: `Session with ${session.interactions.length} interactions`,
      concepts: Array.from(new Set(session.conceptsExplored)),
      duration: session.duration || 0,
      engagement: avgEngagement,
      complexity: this.calculateSessionComplexity(session)
    });
  }

  private calculateSessionComplexity(session: UserSession): number {
    // Analyze complexity based on concepts explored and interaction types
    let complexity = 5; // Base complexity
    
    // Advanced concepts increase complexity
    const advancedConcepts = ['var', 'convexity', 'sharpe_ratio', 'alpha', 'beta'];
    const advancedCount = session.conceptsExplored.filter(concept => 
      advancedConcepts.includes(concept)
    ).length;
    
    complexity += advancedCount * 1.5;
    
    // Simulation usage indicates higher complexity
    complexity += session.simulationsRun * 0.5;
    
    return Math.min(10, complexity);
  }

  private async updateBehavioralPatterns(session: UserSession): Promise<void> {
    const userId = session.userId;
    let pattern = this.behavioralPatterns.get(userId);
    
    if (!pattern) {
      pattern = await this.initializeBehavioralPattern(userId);
    }

    // Update session frequency (exponential moving average)
    const sessionHour = session.startTime.getHours();
    const sessionDay = session.startTime.getDay();
    
    pattern.patterns.preferredTimeOfDay = 
      (pattern.patterns.preferredTimeOfDay * 0.8) + (sessionHour * 0.2);
    
    pattern.patterns.preferredDayOfWeek = 
      (pattern.patterns.preferredDayOfWeek * 0.8) + (sessionDay * 0.2);
    
    // Update average session duration
    if (session.duration) {
      pattern.patterns.averageSessionDuration = 
        (pattern.patterns.averageSessionDuration * 0.9) + (session.duration * 0.1);
    }
    
    // Update learning velocity
    const uniqueConcepts = new Set(session.conceptsExplored).size;
    const sessionLearningVelocity = uniqueConcepts / (session.duration || 1);
    pattern.patterns.learningVelocity = 
      (pattern.patterns.learningVelocity * 0.8) + (sessionLearningVelocity * 0.2);
    
    // Update topic preferences
    for (const concept of session.conceptsExplored) {
      pattern.patterns.topicPreferences[concept] = 
        (pattern.patterns.topicPreferences[concept] || 0) + 1;
    }
    
    // Update interaction mode preferences
    const modeCount = session.interactions.reduce((acc, int) => {
      acc[int.type] = (acc[int.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    for (const [mode, count] of Object.entries(modeCount)) {
      pattern.patterns.interactionModes[mode] = 
        (pattern.patterns.interactionModes[mode] || 0) + count;
    }
    
    // Update engagement trend
    const avgEngagement = session.interactions.reduce((sum, int) => 
      sum + int.engagement, 0) / session.interactions.length;
    
    pattern.patterns.engagementTrend.push(avgEngagement);
    if (pattern.patterns.engagementTrend.length > 10) {
      pattern.patterns.engagementTrend.shift();
    }
    
    // Generate predictions
    pattern.predictions = await this.generatePredictions(pattern);
    
    this.behavioralPatterns.set(userId, pattern);
    await this.storeBehavioralPattern(pattern);
  }

  private async initializeBehavioralPattern(userId: string): Promise<BehavioralPattern> {
    return {
      userId,
      patterns: {
        sessionFrequency: 3, // Default 3 sessions per week
        averageSessionDuration: 15, // Default 15 minutes
        preferredTimeOfDay: 19, // Default 7 PM
        preferredDayOfWeek: 2, // Default Tuesday
        learningVelocity: 2, // Default 2 concepts per session
        questionComplexity: 5, // Default medium complexity
        engagementTrend: [0.5], // Default medium engagement
        topicPreferences: {},
        interactionModes: {}
      },
      predictions: {
        nextSessionTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        likelyTopics: [],
        recommendedComplexity: 5,
        churnRisk: 0.3,
        learningPotential: 0.7
      }
    };
  }

  private async generatePredictions(pattern: BehavioralPattern): Promise<BehavioralPattern['predictions']> {
    // Predict next session time based on frequency and preferred time
    const daysUntilNext = 7 / pattern.patterns.sessionFrequency;
    const nextSessionTime = new Date();
    nextSessionTime.setDate(nextSessionTime.getDate() + daysUntilNext);
    nextSessionTime.setHours(Math.round(pattern.patterns.preferredTimeOfDay));
    
    // Predict likely topics based on preferences and learning gaps
    const userVector = await memorySystem.getUserVector(pattern.userId);
    const topicScores = Object.entries(pattern.patterns.topicPreferences)
      .map(([topic, count]) => ({
        topic,
        score: count * (1 - (userVector.conceptMastery[topic] || 0))
      }))
      .sort((a, b) => b.score - a.score);
    
    const likelyTopics = topicScores.slice(0, 3).map(item => item.topic);
    
    // Calculate churn risk based on engagement trend
    const recentEngagement = pattern.patterns.engagementTrend.slice(-3);
    const avgRecentEngagement = recentEngagement.reduce((a, b) => a + b, 0) / recentEngagement.length;
    const churnRisk = Math.max(0, 1 - avgRecentEngagement * 2);
    
    // Calculate learning potential based on velocity and engagement
    const learningPotential = Math.min(1, 
      (pattern.patterns.learningVelocity / 5) * avgRecentEngagement
    );
    
    // Recommend complexity based on current mastery and preferences
    const avgMastery = Object.values(userVector.conceptMastery || {})
      .reduce((a: number, b: number) => a + b, 0) / 
      Object.keys(userVector.conceptMastery || {}).length;
    
    const recommendedComplexity = Math.round(3 + avgMastery * 7); // 3-10 scale
    
    return {
      nextSessionTime,
      likelyTopics,
      recommendedComplexity,
      churnRisk,
      learningPotential
    };
  }

  async generateLearningInsights(userId: string): Promise<LearningInsight[]> {
    const pattern = this.behavioralPatterns.get(userId);
    const userVector = await memorySystem.getUserVector(userId);
    
    if (!pattern) return [];
    
    const insights: LearningInsight[] = [];
    
    // Strength identification
    const strongConcepts = Object.entries(userVector.conceptMastery || {})
      .filter(([_, mastery]) => (mastery as number) > 0.8)
      .map(([concept, _]) => concept);
    
    if (strongConcepts.length > 0) {
      insights.push({
        userId,
        type: 'strength',
        title: 'Strong Foundation Established',
        description: `You've mastered ${strongConcepts.length} key concepts: ${strongConcepts.slice(0, 3).join(', ')}`,
        confidence: 0.9,
        actionable: true,
        relatedConcepts: strongConcepts,
        suggestedActions: ['Explore advanced applications', 'Teach others these concepts', 'Apply to real scenarios']
      });
    }
    
    // Learning velocity insight
    if (pattern.patterns.learningVelocity > 3) {
      insights.push({
        userId,
        type: 'strength',
        title: 'Rapid Learning Pace',
        description: 'Your learning velocity is above average. You absorb new concepts quickly.',
        confidence: 0.8,
        actionable: true,
        relatedConcepts: [],
        suggestedActions: ['Challenge yourself with advanced topics', 'Consider accelerated learning paths']
      });
    }
    
    // Engagement trend analysis
    const recentEngagement = pattern.patterns.engagementTrend.slice(-5);
    const engagementTrend = recentEngagement[recentEngagement.length - 1] - recentEngagement[0];
    
    if (engagementTrend < -0.2) {
      insights.push({
        userId,
        type: 'weakness',
        title: 'Declining Engagement',
        description: 'Your engagement has decreased in recent sessions. Consider varying your learning approach.',
        confidence: 0.7,
        actionable: true,
        relatedConcepts: [],
        suggestedActions: ['Try voice interactions', 'Explore practical simulations', 'Take a short break']
      });
    }
    
    // Topic preference insights
    const topTopics = Object.entries(pattern.patterns.topicPreferences)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 2)
      .map(([topic, _]) => topic);
    
    if (topTopics.length > 0) {
      insights.push({
        userId,
        type: 'opportunity',
        title: 'Specialized Interest Detected',
        description: `You show strong interest in ${topTopics.join(' and ')}. Consider specializing further.`,
        confidence: 0.8,
        actionable: true,
        relatedConcepts: topTopics,
        suggestedActions: ['Explore advanced applications', 'Find real-world case studies', 'Connect with experts']
      });
    }
    
    return insights;
  }

  private async performRealTimeAnalysis(session: UserSession): Promise<void> {
    // Real-time analysis for immediate feedback
    const recentInteractions = session.interactions.slice(-5);
    const avgRecentEngagement = recentInteractions.reduce((sum, int) => 
      sum + int.engagement, 0) / recentInteractions.length;
    
    // If engagement drops significantly, trigger intervention
    if (avgRecentEngagement < 0.3 && recentInteractions.length >= 3) {
      await this.triggerEngagementIntervention(session.userId, session.sessionId);
    }
    
    // If user is exploring advanced concepts rapidly, suggest deeper dive
    const recentConcepts = recentInteractions
      .flatMap(int => int.data.concepts || [])
      .filter(concept => ['var', 'convexity', 'sharpe_ratio'].includes(concept));
    
    if (recentConcepts.length >= 2) {
      await this.suggestDeepDive(session.userId, recentConcepts);
    }
  }

  private async triggerEngagementIntervention(userId: string, sessionId: string): Promise<void> {
    // Cache intervention suggestion for the chat service to pick up
    await cacheService.set(`intervention_${sessionId}`, {
      type: 'engagement_boost',
      suggestions: [
        'Would you like to try a different learning approach?',
        'Let\'s explore this with a practical example',
        'How about we run a quick simulation?'
      ],
      timestamp: new Date()
    }, 300); // 5 minutes
  }

  private async suggestDeepDive(userId: string, concepts: string[]): Promise<void> {
    const userVector = await memorySystem.getUserVector(userId);
    
    // Only suggest if user has sufficient foundation
    const hasFoundation = concepts.every(concept => 
      (userVector.conceptMastery[concept] || 0) > 0.3
    );
    
    if (hasFoundation) {
      await cacheService.set(`deep_dive_${userId}`, {
        type: 'advanced_exploration',
        concepts,
        suggestions: [
          'Ready to explore the mathematical foundations?',
          'Let\'s see how this applies to real portfolios',
          'Want to run an advanced simulation?'
        ],
        timestamp: new Date()
      }, 600); // 10 minutes
    }
  }

  private async updateSessionFrequency(userId: string): Promise<void> {
    // Track session frequency for behavioral analysis
    const cacheKey = `session_frequency_${userId}`;
    const sessionHistory = await cacheService.get(cacheKey) || [];
    
    sessionHistory.push(new Date());
    
    // Keep only last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentSessions = sessionHistory.filter(date => new Date(date) > thirtyDaysAgo);
    
    await cacheService.set(cacheKey, recentSessions, 86400 * 30); // 30 days
  }

  private async storeSession(session: UserSession): Promise<void> {
    // Store session data for long-term analysis
    await cacheService.set(`session_${session.sessionId}`, session, 86400 * 7); // 7 days
  }

  private async storeBehavioralPattern(pattern: BehavioralPattern): Promise<void> {
    // Store behavioral pattern for persistence
    await cacheService.set(`behavior_${pattern.userId}`, pattern, 86400 * 30); // 30 days
  }

  async getBehavioralPattern(userId: string): Promise<BehavioralPattern | null> {
    let pattern = this.behavioralPatterns.get(userId);
    
    if (!pattern) {
      pattern = await cacheService.get(`behavior_${userId}`);
      if (pattern) {
        this.behavioralPatterns.set(userId, pattern);
      }
    }
    
    return pattern;
  }

  async getAnalyticsDashboard(userId: string): Promise<{
    sessionStats: any;
    learningProgress: any;
    behavioralInsights: any;
    predictions: any;
  }> {
    const pattern = await this.getBehavioralPattern(userId);
    const userVector = await memorySystem.getUserVector(userId);
    const insights = await this.generateLearningInsights(userId);
    
    return {
      sessionStats: {
        frequency: pattern?.patterns.sessionFrequency || 0,
        averageDuration: pattern?.patterns.averageSessionDuration || 0,
        totalSessions: pattern?.patterns.engagementTrend.length || 0,
        engagementTrend: pattern?.patterns.engagementTrend || []
      },
      learningProgress: {
        conceptsMastered: Object.keys(userVector.conceptMastery || {}).length,
        averageMastery: Object.values(userVector.conceptMastery || {})
          .reduce((a: number, b: number) => a + b, 0) / 
          Object.keys(userVector.conceptMastery || {}).length,
        learningVelocity: pattern?.patterns.learningVelocity || 0,
        topTopics: Object.entries(pattern?.patterns.topicPreferences || {})
          .sort(([,a], [,b]) => (b as number) - (a as number))
          .slice(0, 5)
      },
      behavioralInsights: insights,
      predictions: pattern?.predictions || null
    };
  }
}

export const userAnalyticsService = new UserAnalyticsService();