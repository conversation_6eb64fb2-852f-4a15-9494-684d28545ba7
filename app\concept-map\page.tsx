"use client";

import { useState } from 'react';
import ConceptMap from '@/components/ConceptMap';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  Network, 
  TrendingUp, 
  Target, 
  Lightbulb,
  BookOpen,
  Zap,
  Award
} from 'lucide-react';

export default function ConceptMapPage() {
  const [selectedConcept, setSelectedConcept] = useState<string | null>(null);
  const [hoveredConcept, setHoveredConcept] = useState<string | null>(null);

  const conceptDetails = {
    'diversification': {
      title: 'Diversification',
      description: 'The practice of spreading investments across various assets to reduce risk.',
      keyPoints: [
        'Reduces portfolio volatility',
        'Protects against specific asset risks',
        'Improves risk-adjusted returns'
      ],
      realWorldExample: 'Instead of investing all money in one stock, spread it across stocks, bonds, and real estate.',
      nextSteps: ['Learn about correlation', 'Study asset allocation', 'Practice portfolio construction']
    },
    'asset_allocation': {
      title: 'Asset Allocation',
      description: 'Strategic distribution of investments across different asset classes.',
      keyPoints: [
        'Determines portfolio risk and return',
        'Should match investor goals and timeline',
        'Requires periodic rebalancing'
      ],
      realWorldExample: 'A 30-year-old might use 70% stocks, 20% bonds, 10% alternatives.',
      nextSteps: ['Understand rebalancing', 'Learn about strategic vs tactical allocation', 'Study lifecycle investing']
    },
    'sharpe_ratio': {
      title: 'Sharpe Ratio',
      description: 'Measures risk-adjusted return by comparing excess return to volatility.',
      keyPoints: [
        'Higher ratio indicates better risk-adjusted performance',
        'Useful for comparing investments',
        'Considers both return and risk'
      ],
      realWorldExample: 'Fund A returns 12% with 15% volatility vs Fund B returns 10% with 8% volatility.',
      nextSteps: ['Learn about other risk metrics', 'Study portfolio optimization', 'Practice calculation methods']
    }
  };

  const learningPaths = [
    {
      id: 'beginner',
      title: 'Foundation Builder',
      description: 'Start with fundamental concepts',
      concepts: ['diversification', 'asset_allocation', 'risk_return'],
      duration: '2-3 weeks',
      difficulty: 'Beginner'
    },
    {
      id: 'intermediate',
      title: 'Portfolio Strategist',
      description: 'Advanced portfolio management',
      concepts: ['portfolio_theory', 'sharpe_ratio', 'rebalancing'],
      duration: '4-6 weeks',
      difficulty: 'Intermediate'
    },
    {
      id: 'advanced',
      title: 'Risk Master',
      description: 'Sophisticated risk analysis',
      concepts: ['var', 'convexity', 'stress_testing'],
      duration: '6-8 weeks',
      difficulty: 'Advanced'
    }
  ];

  const achievements = [
    {
      id: 'first_concept',
      title: 'First Steps',
      description: 'Mastered your first financial concept',
      icon: <BookOpen className="h-5 w-5" />,
      unlocked: true
    },
    {
      id: 'diversification_master',
      title: 'Diversification Expert',
      description: 'Achieved 90%+ mastery in diversification',
      icon: <Target className="h-5 w-5" />,
      unlocked: true
    },
    {
      id: 'connection_finder',
      title: 'Connection Finder',
      description: 'Discovered 5 concept relationships',
      icon: <Network className="h-5 w-5" />,
      unlocked: false
    },
    {
      id: 'advanced_learner',
      title: 'Advanced Learner',
      description: 'Unlocked advanced concepts',
      icon: <Brain className="h-5 w-5" />,
      unlocked: false
    }
  ];

  const handleNodeClick = (nodeId: string) => {
    setSelectedConcept(nodeId);
  };

  const handleNodeHover = (nodeId: string | null) => {
    setHoveredConcept(nodeId);
  };

  const selectedConceptData = selectedConcept ? conceptDetails[selectedConcept] : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl font-bold text-white">Knowledge Network</h1>
            <p className="text-slate-300 max-w-2xl mx-auto">
              Explore the interconnected world of finance through our dynamic concept map. 
              Watch your knowledge grow and discover new connections as you learn.
            </p>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
            
            {/* Concept Map - Takes up most space */}
            <div className="xl:col-span-3">
              <ConceptMap 
                userId="demo-user" 
                onNodeClick={handleNodeClick}
                onNodeHover={handleNodeHover}
              />
            </div>

            {/* Sidebar */}
            <div className="xl:col-span-1 space-y-6">
              
              {/* Learning Stats */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-400" />
                    Progress
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">4</div>
                      <div className="text-xs text-slate-400">Concepts Mastered</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">67%</div>
                      <div className="text-xs text-slate-400">Average Mastery</div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-300">Knowledge Network</span>
                      <span className="text-white">Level 3</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full" style={{ width: '67%' }}></div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-400" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700" size="sm">
                    <Lightbulb className="h-4 w-4 mr-2" />
                    Get Learning Suggestion
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600" size="sm">
                    <Target className="h-4 w-4 mr-2" />
                    Take Knowledge Quiz
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600" size="sm">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Study Weak Areas
                  </Button>
                </CardContent>
              </Card>

              {/* Achievements */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Award className="h-5 w-5 text-yellow-400" />
                    Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {achievements.map((achievement) => (
                    <div key={achievement.id} className={`flex items-center gap-3 p-2 rounded-lg ${
                      achievement.unlocked ? 'bg-yellow-500/10 border border-yellow-500/30' : 'bg-slate-700/30'
                    }`}>
                      <div className={`${achievement.unlocked ? 'text-yellow-400' : 'text-slate-500'}`}>
                        {achievement.icon}
                      </div>
                      <div className="flex-1">
                        <div className={`text-sm font-medium ${achievement.unlocked ? 'text-white' : 'text-slate-400'}`}>
                          {achievement.title}
                        </div>
                        <div className="text-xs text-slate-500">
                          {achievement.description}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Detailed Concept Information */}
          {selectedConceptData && (
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">{selectedConceptData.title}</CardTitle>
                <CardDescription className="text-slate-400">
                  {selectedConceptData.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="overview" className="space-y-4">
                  <TabsList className="bg-slate-700 border-slate-600">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="example">Real Example</TabsTrigger>
                    <TabsTrigger value="next">Next Steps</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="overview" className="space-y-4">
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-3">Key Points</h4>
                      <ul className="space-y-2">
                        {selectedConceptData.keyPoints.map((point, index) => (
                          <li key={index} className="text-slate-300 flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                            {point}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="example" className="space-y-4">
                    <div className="bg-slate-700/50 rounded-lg p-4">
                      <h4 className="text-lg font-semibold text-white mb-2">Real-World Application</h4>
                      <p className="text-slate-300">{selectedConceptData.realWorldExample}</p>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="next" className="space-y-4">
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-3">Recommended Next Steps</h4>
                      <div className="space-y-2">
                        {selectedConceptData.nextSteps.map((step, index) => (
                          <div key={index} className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg">
                            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                              {index + 1}
                            </div>
                            <span className="text-slate-300">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}

          {/* Learning Paths */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">Recommended Learning Paths</CardTitle>
              <CardDescription className="text-slate-400">
                Structured pathways to build your financial expertise
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {learningPaths.map((path) => (
                  <div key={path.id} className="bg-slate-700/30 rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-white">{path.title}</h3>
                      <Badge variant="outline" className={`${
                        path.difficulty === 'Beginner' ? 'border-green-500/30 text-green-300' :
                        path.difficulty === 'Intermediate' ? 'border-yellow-500/30 text-yellow-300' :
                        'border-red-500/30 text-red-300'
                      }`}>
                        {path.difficulty}
                      </Badge>
                    </div>
                    <p className="text-slate-400 text-sm">{path.description}</p>
                    <div className="space-y-2">
                      <div className="text-xs text-slate-500">Duration: {path.duration}</div>
                      <div className="text-xs text-slate-500">
                        Concepts: {path.concepts.length}
                      </div>
                    </div>
                    <Button size="sm" className="w-full bg-blue-600 hover:bg-blue-700">
                      Start Path
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}