"use client";

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Mi<PERSON>, Mic<PERSON>ff, Send, Brain, Lightbulb, TrendingUp, <PERSON><PERSON>hart, Bot, User, Loader2, AlertTriangle } from 'lucide-react';
import { geminiClient, type ChatMessage as GeminiChatMessage } from '@/lib/gemini';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Welcome! I'm your AI Financial Strategist, powered by advanced AI to provide you with expert financial guidance. I specialize in helping you with:\n\n💰 **Personal Budgeting & Expense Management**\n📈 **Investment Strategies & Portfolio Analysis**\n🏦 **Retirement Planning & Tax Optimization**\n💳 **Debt Management & Credit Improvement**\n🎯 **Financial Goal Setting & Achievement**\n\nI'll provide detailed explanations, real-world examples, and actionable steps for all your financial questions. What financial topic would you like to explore today?",
      timestamp: new Date(),
      suggestions: [
        "Create a personalized budget plan",
        "Explain investment diversification with examples",
        "Calculate compound interest scenarios",
        "Design a retirement savings strategy"
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // @ts-ignore: SpeechRecognition is a browser API, not in TypeScript DOM lib by default
    const recognition = useRef<(typeof window extends { SpeechRecognition: any } ? InstanceType<typeof window.SpeechRecognition> : InstanceType<typeof window.webkitSpeechRecognition>) | null>(null);

  const promptSuggestions = [
    {
      category: "Investment & Portfolio",
      icon: <TrendingUp className="h-4 w-4" />,
      prompts: [
        "Explain dollar-cost averaging with a $500/month example",
        "Compare ETFs vs mutual funds with real examples",
        "How to build a diversified portfolio with $10,000?",
        "Calculate potential returns: $1000 monthly for 20 years",
        "What's the difference between growth and value stocks?",
        "Should I invest in international markets?"
      ]
    },
    {
      category: "Budgeting & Savings",
      icon: <PieChart className="h-4 w-4" />,
      prompts: [
        "Create a budget for $60,000 annual income",
        "How to save $10,000 in one year?",
        "Track expenses effectively: best methods",
        "Emergency fund: how much and where to keep it?",
        "Reduce monthly expenses by 20%: practical tips",
        "High-yield savings vs money market accounts"
      ]
    },
    {
      category: "Debt & Credit",
      icon: <Brain className="h-4 w-4" />,
      prompts: [
        "Debt snowball vs avalanche: which is better?",
        "Improve credit score from 650 to 750",
        "Should I consolidate my credit card debt?",
        "Student loan repayment strategies",
        "Mortgage vs rent: financial comparison",
        "How to negotiate with creditors effectively"
      ]
    },
    {
      category: "Retirement Planning",
      icon: <Lightbulb className="h-4 w-4" />,
      prompts: [
        "Calculate retirement needs for $80,000 lifestyle",
        "401(k) vs Roth IRA: which should I prioritize?",
        "Maximize employer 401(k) matching strategies",
        "Retirement withdrawal strategies to minimize taxes",
        "Social Security optimization techniques",
        "Catch-up contributions for people over 50"
      ]
    }
  ];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'en-US';

      recognition.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setInputValue(transcript);
        setIsListening(false);
      };

      recognition.current.onerror = () => {
        setIsListening(false);
      };

      recognition.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Check if the query is finance-related
    if (!isFinanceRelated(content.trim())) {
      const warningMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: "I'm your AI Financial Strategist, and I'm specifically designed to help with financial questions and planning. Please ask me about topics like budgeting, investing, retirement planning, debt management, or other financial matters. How can I assist you with your financial goals today?",
        timestamp: new Date(),
        suggestions: [
          "Help me create a budget plan",
          "Explain investment strategies",
          "How to build an emergency fund",
          "Retirement planning advice"
        ]
      };
      setMessages(prev => [...prev, warningMessage]);
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Convert messages to Gemini format
      const chatHistory: GeminiChatMessage[] = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Add the new user message
      chatHistory.push({
        role: 'user',
        content: content.trim()
      });

      // Call Gemini API with financial context
      const response = await geminiClient.generateContent(chatHistory, {
        userBehavior: { focusArea: 'financial_planning' },
        financialProfile: { experienceLevel: 'mixed' }
      });

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        suggestions: response.suggestions || generateFinancialSuggestions(content)
      };

      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('Error calling Gemini API:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: "I apologize, but I'm experiencing technical difficulties. Please try again in a moment. In the meantime, I can help you with budgeting, investment strategies, retirement planning, or any other financial questions you might have.",
        timestamp: new Date(),
        suggestions: [
          "Help me understand compound interest",
          "Explain different investment types",
          "How to improve my credit score",
          "Create a debt payoff strategy"
        ]
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if the user's query is finance-related
  const isFinanceRelated = (input: string): boolean => {
    const financeKeywords = [
      // Core financial terms
      'budget', 'money', 'finance', 'financial', 'investment', 'invest', 'portfolio', 'stock', 'bond',
      'retirement', 'pension', 'savings', 'save', 'debt', 'loan', 'credit', 'mortgage', 'insurance',
      'tax', 'taxes', 'income', 'salary', 'expense', 'spending', 'cost', 'price', 'value', 'worth',

      // Investment terms
      'etf', 'mutual fund', 'index fund', 'dividend', 'yield', 'return', 'roi', 'risk', 'diversification',
      'asset', 'allocation', 'rebalance', 'compound', 'interest', 'inflation', 'market', 'trading',

      // Banking and accounts
      'bank', 'account', 'checking', 'savings', 'cd', 'certificate', 'deposit', 'withdrawal', 'transfer',
      'ira', '401k', 'roth', 'traditional', 'hsa', 'emergency fund',

      // Financial planning
      'goal', 'planning', 'strategy', 'advice', 'guidance', 'recommendation', 'analysis', 'calculation',
      'net worth', 'cash flow', 'liquidity', 'solvency', 'bankruptcy', 'foreclosure',

      // Economic terms
      'economy', 'economic', 'recession', 'bull market', 'bear market', 'volatility', 'gdp',
      'federal reserve', 'interest rate', 'monetary policy'
    ];

    const lowerInput = input.toLowerCase();
    return financeKeywords.some(keyword => lowerInput.includes(keyword)) ||
           // Also check for financial question patterns
           /how much|how to|what is|explain|calculate|compare|should i|can i|help me/i.test(input);
  };

  // Generate contextual financial suggestions based on user input
  const generateFinancialSuggestions = (input: string): string[] => {
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('budget') || lowerInput.includes('spending')) {
      return [
        "Show me the 50/30/20 budgeting rule",
        "How to track expenses effectively?",
        "What budgeting apps do you recommend?",
        "Help me reduce monthly expenses"
      ];
    }

    if (lowerInput.includes('invest') || lowerInput.includes('portfolio') || lowerInput.includes('stock')) {
      return [
        "Explain dollar-cost averaging strategy",
        "What's the difference between ETFs and mutual funds?",
        "How to diversify my investment portfolio?",
        "Calculate potential investment returns"
      ];
    }

    if (lowerInput.includes('retirement') || lowerInput.includes('401k') || lowerInput.includes('ira')) {
      return [
        "Calculate my retirement savings needs",
        "Explain Roth vs Traditional IRA",
        "How to maximize employer 401(k) matching?",
        "Retirement withdrawal strategies"
      ];
    }

    if (lowerInput.includes('debt') || lowerInput.includes('loan') || lowerInput.includes('credit')) {
      return [
        "Debt snowball vs avalanche method",
        "How to improve my credit score?",
        "Should I consolidate my debts?",
        "Strategies for paying off student loans"
      ];
    }

    if (lowerInput.includes('emergency') || lowerInput.includes('savings')) {
      return [
        "How much should I save for emergencies?",
        "Best high-yield savings accounts",
        "Building an emergency fund step by step",
        "Where to keep emergency savings?"
      ];
    }

    // Default financial suggestions
    return [
      "Help me create a comprehensive financial plan",
      "Explain compound interest with examples",
      "How to set and achieve financial goals?",
      "Basic principles of personal finance"
    ];
  };

  const toggleListening = () => {
    if (!recognition.current) return;

    if (isListening) {
      recognition.current.stop();
      setIsListening(false);
    } else {
      recognition.current.start();
      setIsListening(true);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
          
          {/* Sidebar - Prompt Suggestions */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm h-full">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-400" />
                  Smart Prompts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[calc(100vh-16rem)]">
                  <div className="space-y-6">
                    {promptSuggestions.map((category, index) => (
                      <div key={index} className="space-y-3">
                        <div className="flex items-center gap-2">
                          {category.icon}
                          <h3 className="text-sm font-medium text-slate-300">{category.category}</h3>
                        </div>
                        <div className="space-y-2">
                          {category.prompts.map((prompt, promptIndex) => (
                            <Button
                              key={promptIndex}
                              variant="ghost"
                              size="sm"
                              className="text-xs text-slate-400 hover:text-slate-200 h-auto p-2 justify-start whitespace-normal text-left"
                              onClick={() => handleSendMessage(prompt)}
                            >
                              {prompt}
                            </Button>
                          ))}
                        </div>
                        {index < promptSuggestions.length - 1 && (
                          <Separator className="bg-slate-700" />
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Main Chat Area */}
          <div className="lg:col-span-3">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm h-full flex flex-col">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <Bot className="h-5 w-5 text-blue-400" />
                    AI Financial Strategist
                  </CardTitle>
                  <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                    Online
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <ScrollArea className="flex-1 pr-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div key={message.id} className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`flex gap-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            message.role === 'user' ? 'bg-blue-600' : 'bg-slate-700'
                          }`}>
                            {message.role === 'user' ? (
                              <User className="h-4 w-4 text-white" />
                            ) : (
                              <Bot className="h-4 w-4 text-blue-400" />
                            )}
                          </div>
                          <div className="space-y-2">
                            <div className={`p-3 rounded-lg ${
                              message.role === 'user' 
                                ? 'bg-blue-600 text-white' 
                                : 'bg-slate-700 text-slate-200'
                            }`}>
                              <p className="text-sm leading-relaxed">{message.content}</p>
                            </div>
                            {message.suggestions && message.suggestions.length > 0 && (
                              <div className="flex flex-wrap gap-2">
                                {message.suggestions.map((suggestion, index) => (
                                  <Button
                                    key={index}
                                    variant="outline"
                                    size="sm"
                                    className="text-xs border-slate-600 text-slate-300 hover:bg-slate-700"
                                    onClick={() => handleSendMessage(suggestion)}
                                  >
                                    {suggestion}
                                  </Button>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {isLoading && (
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center">
                          <Bot className="h-4 w-4 text-blue-400" />
                        </div>
                        <div className="bg-slate-700 p-3 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
                            <span className="text-sm text-slate-300">Analyzing your request...</span>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                {/* Input Area */}
                <div className="mt-4 space-y-4">
                  <Separator className="bg-slate-700" />
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <Input
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder="Ask me anything about your finances..."
                        className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage(inputValue);
                          }
                        }}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className={`border-slate-600 ${isListening ? 'bg-red-600 text-white' : 'text-slate-300 hover:bg-slate-700'}`}
                      onClick={toggleListening}
                    >
                      {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    </Button>
                    <Button
                      onClick={() => handleSendMessage(inputValue)}
                      disabled={!inputValue.trim() || isLoading}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}