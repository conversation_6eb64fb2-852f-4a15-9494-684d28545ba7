"use client";

import { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, Send, Brain, Lightbulb, TrendingUp, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, User, Loader2 } from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: "Hello! I'm your AI Financial Strategist. I'm here to help you navigate the complex world of finance with personalized guidance. What would you like to explore today?",
      timestamp: new Date(),
      suggestions: [
        "Help me create a budget",
        "Analyze my investment portfolio",
        "Explain compound interest",
        "Plan for retirement"
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const recognition = useRef<SpeechRecognition | null>(null);

  const promptSuggestions = [
    {
      category: "Investment Strategy",
      icon: <TrendingUp className="h-4 w-4" />,
      prompts: [
        "What's the difference between growth and value investing?",
        "How do I diversify my portfolio effectively?",
        "Should I invest in index funds or individual stocks?",
        "What's the ideal asset allocation for my age?"
      ]
    },
    {
      category: "Risk Management",
      icon: <PieChart className="h-4 w-4" />,
      prompts: [
        "How do I assess my risk tolerance?",
        "What are the main types of investment risks?",
        "How much should I keep in emergency funds?",
        "When should I rebalance my portfolio?"
      ]
    },
    {
      category: "Financial Planning",
      icon: <Brain className="h-4 w-4" />,
      prompts: [
        "How do I create a retirement savings plan?",
        "What's the 50/30/20 budgeting rule?",
        "How do I pay off debt strategically?",
        "What are tax-advantaged investment accounts?"
      ]
    }
  ];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = false;
      recognition.current.interimResults = false;
      recognition.current.lang = 'en-US';

      recognition.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputValue(transcript);
        setIsListening(false);
      };

      recognition.current.onerror = () => {
        setIsListening(false);
      };

      recognition.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response (replace with actual Gemini API call)
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: generateAIResponse(content),
        timestamp: new Date(),
        suggestions: generateSuggestions(content)
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1500);
  };

  const generateAIResponse = (input: string): string => {
    const responses = {
      budget: "Creating a budget is fundamental to financial success. I recommend the 50/30/20 rule as a starting point: 50% for needs (housing, utilities, groceries), 30% for wants (entertainment, dining out), and 20% for savings and debt repayment. Would you like me to help you customize this based on your income and goals?",
      investment: "Investment strategy should align with your risk tolerance, time horizon, and financial goals. For beginners, I often recommend starting with low-cost index funds for broad market exposure. The key principles are diversification, consistent contributions, and staying invested for the long term. What's your current investment experience level?",
      retirement: "Retirement planning is about starting early and being consistent. The power of compound interest means that time is your greatest asset. I recommend maximizing employer 401(k) matches first, then contributing to IRAs. A good rule of thumb is to save 10-15% of your income for retirement. How many years do you have until retirement?",
      default: "That's an excellent question! Financial planning involves many interconnected concepts. Let me break this down in a way that's practical and actionable for your situation. The key is to understand not just the 'what' but the 'why' behind each financial decision."
    };

    const lowerInput = input.toLowerCase();
    if (lowerInput.includes('budget')) return responses.budget;
    if (lowerInput.includes('invest') || lowerInput.includes('portfolio')) return responses.investment;
    if (lowerInput.includes('retirement')) return responses.retirement;
    return responses.default;
  };

  const generateSuggestions = (input: string): string[] => {
    const suggestions = {
      budget: ["Show me budget tracking tools", "How do I reduce expenses?", "What if my income varies?"],
      investment: ["Explain risk vs return", "How to start with $1000?", "Compare ETFs vs mutual funds"],
      retirement: ["Calculate my retirement needs", "Explain Roth vs Traditional IRA", "How to catch up on savings?"],
      default: ["Tell me about emergency funds", "Explain debt consolidation", "How to improve credit score"]
    };

    const lowerInput = input.toLowerCase();
    if (lowerInput.includes('budget')) return suggestions.budget;
    if (lowerInput.includes('invest') || lowerInput.includes('portfolio')) return suggestions.investment;
    if (lowerInput.includes('retirement')) return suggestions.retirement;
    return suggestions.default;
  };

  const toggleListening = () => {
    if (!recognition.current) return;

    if (isListening) {
      recognition.current.stop();
      setIsListening(false);
    } else {
      recognition.current.start();
      setIsListening(true);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]">
          
          {/* Sidebar - Prompt Suggestions */}
          <div className="lg:col-span-1">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm h-full">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-400" />
                  Smart Prompts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[calc(100vh-16rem)]">
                  <div className="space-y-6">
                    {promptSuggestions.map((category, index) => (
                      <div key={index} className="space-y-3">
                        <div className="flex items-center gap-2">
                          {category.icon}
                          <h3 className="text-sm font-medium text-slate-300">{category.category}</h3>
                        </div>
                        <div className="space-y-2">
                          {category.prompts.map((prompt, promptIndex) => (
                            <Button
                              key={promptIndex}
                              variant="ghost"
                              size="sm"
                              className="text-xs text-slate-400 hover:text-slate-200 h-auto p-2 justify-start whitespace-normal text-left"
                              onClick={() => handleSendMessage(prompt)}
                            >
                              {prompt}
                            </Button>
                          ))}
                        </div>
                        {index < promptSuggestions.length - 1 && (
                          <Separator className="bg-slate-700" />
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Main Chat Area */}
          <div className="lg:col-span-3">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm h-full flex flex-col">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <Bot className="h-5 w-5 text-blue-400" />
                    AI Financial Strategist
                  </CardTitle>
                  <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                    Online
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <ScrollArea className="flex-1 pr-4">
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div key={message.id} className={`flex gap-3 ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`flex gap-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            message.role === 'user' ? 'bg-blue-600' : 'bg-slate-700'
                          }`}>
                            {message.role === 'user' ? (
                              <User className="h-4 w-4 text-white" />
                            ) : (
                              <Bot className="h-4 w-4 text-blue-400" />
                            )}
                          </div>
                          <div className="space-y-2">
                            <div className={`p-3 rounded-lg ${
                              message.role === 'user' 
                                ? 'bg-blue-600 text-white' 
                                : 'bg-slate-700 text-slate-200'
                            }`}>
                              <p className="text-sm leading-relaxed">{message.content}</p>
                            </div>
                            {message.suggestions && message.suggestions.length > 0 && (
                              <div className="flex flex-wrap gap-2">
                                {message.suggestions.map((suggestion, index) => (
                                  <Button
                                    key={index}
                                    variant="outline"
                                    size="sm"
                                    className="text-xs border-slate-600 text-slate-300 hover:bg-slate-700"
                                    onClick={() => handleSendMessage(suggestion)}
                                  >
                                    {suggestion}
                                  </Button>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {isLoading && (
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center">
                          <Bot className="h-4 w-4 text-blue-400" />
                        </div>
                        <div className="bg-slate-700 p-3 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
                            <span className="text-sm text-slate-300">Analyzing your request...</span>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                {/* Input Area */}
                <div className="mt-4 space-y-4">
                  <Separator className="bg-slate-700" />
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <Input
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder="Ask me anything about your finances..."
                        className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                        onKeyPress={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage(inputValue);
                          }
                        }}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className={`border-slate-600 ${isListening ? 'bg-red-600 text-white' : 'text-slate-300 hover:bg-slate-700'}`}
                      onClick={toggleListening}
                    >
                      {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    </Button>
                    <Button
                      onClick={() => handleSendMessage(inputValue)}
                      disabled={!inputValue.trim() || isLoading}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}