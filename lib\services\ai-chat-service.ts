// AI Chat Service with Context-Aware Responses
import { geminiClient } from '../gemini';
import { memorySystem } from './memory-system';
import { cacheService } from './cache-service';

export interface ChatContext {
  userId: string;
  sessionId: string;
  conversationHistory: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    concepts?: string[];
    engagement?: number;
  }>;
  currentTopic?: string;
  learningObjective?: string;
}

export interface ChatResponse {
  content: string;
  suggestions: string[];
  conceptsIntroduced: string[];
  nextLearningPath: string[];
  engagementHooks: string[];
  complexity: number;
}

export class AIChatService {
  private activeContexts: Map<string, ChatContext> = new Map();

  async processMessage(
    userId: string,
    message: string,
    sessionId: string
  ): Promise<ChatResponse> {
    // Get or create chat context
    const context = await this.getOrCreateContext(userId, sessionId);
    
    // Get user vector for personalization
    const userVector = await memorySystem.getUserVector(userId);
    
    // Analyze message for concepts and intent
    const messageAnalysis = await this.analyzeMessage(message, userVector);
    
    // Generate contextual response
    const response = await this.generateContextualResponse(
      message,
      context,
      userVector,
      messageAnalysis
    );
    
    // Update conversation history
    context.conversationHistory.push(
      {
        role: 'user',
        content: message,
        timestamp: new Date(),
        concepts: messageAnalysis.concepts
      },
      {
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        concepts: response.conceptsIntroduced,
        engagement: messageAnalysis.engagement
      }
    );
    
    // Record interaction for learning
    await memorySystem.recordInteraction(userId, {
      type: 'question',
      content: message,
      concepts: [...messageAnalysis.concepts, ...response.conceptsIntroduced],
      duration: 5, // Estimated reading time
      engagement: messageAnalysis.engagement,
      complexity: response.complexity
    });
    
    // Cache response for similar queries
    await cacheService.cacheResponse(
      this.generateCacheKey(message, userVector),
      response,
      3600 // 1 hour TTL
    );
    
    return response;
  }

  private async getOrCreateContext(userId: string, sessionId: string): Promise<ChatContext> {
    const contextKey = `${userId}:${sessionId}`;
    
    if (this.activeContexts.has(contextKey)) {
      return this.activeContexts.get(contextKey)!;
    }
    
    // Try to load from cache
    const cachedContext = await cacheService.getContext(contextKey);
    if (cachedContext) {
      this.activeContexts.set(contextKey, cachedContext);
      return cachedContext;
    }
    
    // Create new context
    const newContext: ChatContext = {
      userId,
      sessionId,
      conversationHistory: []
    };
    
    this.activeContexts.set(contextKey, newContext);
    return newContext;
  }

  private async analyzeMessage(message: string, userVector: any): Promise<{
    concepts: string[];
    intent: string;
    complexity: number;
    engagement: number;
    questionType: string;
  }> {
    // Use NLP techniques to extract concepts and intent
    const concepts = this.extractConcepts(message);
    const intent = this.classifyIntent(message);
    const complexity = this.assessComplexity(message);
    const engagement = this.predictEngagement(message, userVector);
    const questionType = this.classifyQuestionType(message);
    
    return { concepts, intent, complexity, engagement, questionType };
  }

  private extractConcepts(message: string): string[] {
    const conceptKeywords = {
      'asset_allocation': ['asset allocation', 'portfolio mix', 'diversification'],
      'sharpe_ratio': ['sharpe ratio', 'risk adjusted return', 'sharpe'],
      'var': ['value at risk', 'var', 'risk measure'],
      'convexity': ['convexity', 'bond convexity', 'duration'],
      'diversification': ['diversification', 'spread risk', 'correlation']
    };
    
    const foundConcepts = [];
    const lowerMessage = message.toLowerCase();
    
    for (const [concept, keywords] of Object.entries(conceptKeywords)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        foundConcepts.push(concept);
      }
    }
    
    return foundConcepts;
  }

  private classifyIntent(message: string): string {
    const intentPatterns = {
      'learn': ['how', 'what', 'explain', 'teach', 'understand'],
      'apply': ['should i', 'recommend', 'advice', 'strategy'],
      'analyze': ['analyze', 'evaluate', 'compare', 'calculate'],
      'explore': ['explore', 'discover', 'show me', 'tell me about']
    };
    
    const lowerMessage = message.toLowerCase();
    
    for (const [intent, patterns] of Object.entries(intentPatterns)) {
      if (patterns.some(pattern => lowerMessage.includes(pattern))) {
        return intent;
      }
    }
    
    return 'general';
  }

  private assessComplexity(message: string): number {
    // Simple heuristic based on financial terminology and question structure
    const complexTerms = ['sharpe', 'var', 'convexity', 'beta', 'alpha', 'volatility'];
    const complexityScore = complexTerms.filter(term => 
      message.toLowerCase().includes(term)
    ).length;
    
    return Math.min(10, 3 + complexityScore * 2);
  }

  private predictEngagement(message: string, userVector: any): number {
    // Predict user engagement based on message characteristics and user history
    let engagement = 0.5; // Base engagement
    
    // Longer, more detailed questions suggest higher engagement
    if (message.length > 100) engagement += 0.2;
    
    // Questions about user's preferred topics
    if (userVector.learningStyle?.topicPreferences?.some(topic => 
      message.toLowerCase().includes(topic)
    )) {
      engagement += 0.3;
    }
    
    return Math.min(1, engagement);
  }

  private classifyQuestionType(message: string): string {
    if (message.includes('?')) {
      if (message.toLowerCase().includes('how')) return 'how-to';
      if (message.toLowerCase().includes('what')) return 'definition';
      if (message.toLowerCase().includes('why')) return 'explanation';
      if (message.toLowerCase().includes('when')) return 'timing';
      if (message.toLowerCase().includes('should')) return 'advice';
    }
    return 'statement';
  }

  private async generateContextualResponse(
    message: string,
    context: ChatContext,
    userVector: any,
    analysis: any
  ): Promise<ChatResponse> {
    // Build enhanced context for Gemini
    const enhancedContext = {
      userBehavior: userVector.behavioralPatterns,
      financialProfile: userVector.riskProfile,
      learningHistory: userVector.conceptMastery,
      conversationContext: context.conversationHistory.slice(-5), // Last 5 exchanges
      currentLearningPath: memorySystem.getRecommendedConcepts(context.userId),
      messageAnalysis: analysis
    };
    
    // Generate response using Gemini with enhanced context
    const geminiResponse = await geminiClient.generateContent(
      [{ role: 'user', content: message }],
      enhancedContext
    );
    
    // Generate learning-focused suggestions
    const suggestions = this.generateSmartSuggestions(analysis, userVector);
    
    // Identify next learning concepts
    const nextLearningPath = this.generateLearningPath(analysis.concepts, userVector);
    
    // Create engagement hooks
    const engagementHooks = this.generateEngagementHooks(analysis, userVector);
    
    return {
      content: geminiResponse.content,
      suggestions,
      conceptsIntroduced: analysis.concepts,
      nextLearningPath,
      engagementHooks,
      complexity: analysis.complexity
    };
  }

  private generateSmartSuggestions(analysis: any, userVector: any): string[] {
    const suggestions = [];
    
    // Concept-specific follow-ups
    if (analysis.concepts.includes('asset_allocation')) {
      suggestions.push(
        'How do I determine my optimal asset allocation?',
        'What factors should influence my portfolio mix?',
        'Show me examples of different allocation strategies'
      );
    }
    
    if (analysis.concepts.includes('sharpe_ratio')) {
      suggestions.push(
        'How do I calculate Sharpe ratio for my portfolio?',
        'What\'s considered a good Sharpe ratio?',
        'Compare Sharpe ratio with other risk metrics'
      );
    }
    
    // Personalized based on learning style
    if (userVector.learningStyle?.preferredComplexity === 'advanced') {
      suggestions.push('Dive deeper into the mathematical foundations');
    } else {
      suggestions.push('Can you explain this with a simple example?');
    }
    
    return suggestions.slice(0, 3);
  }

  private generateLearningPath(currentConcepts: string[], userVector: any): string[] {
    const recommendedConcepts = memorySystem.getRecommendedConcepts(userVector.userId);
    
    // Filter recommendations based on current conversation
    return recommendedConcepts.filter(concept => 
      !currentConcepts.includes(concept)
    ).slice(0, 3);
  }

  private generateEngagementHooks(analysis: any, userVector: any): string[] {
    const hooks = [];
    
    // Based on user's risk profile
    if (userVector.riskProfile?.tolerance > 7) {
      hooks.push('Explore advanced strategies for aggressive investors');
    }
    
    // Based on learning progress
    const masteryLevel = Object.values(userVector.conceptMastery || {}).reduce((a: number, b: number) => a + b, 0);
    if (masteryLevel > 5) {
      hooks.push('Ready for portfolio optimization techniques?');
    }
    
    return hooks;
  }

  private generateCacheKey(message: string, userVector: any): string {
    // Create a hash-like key based on message content and user characteristics
    const userCharacteristics = `${userVector.learningStyle?.preferredComplexity}_${userVector.riskProfile?.tolerance}`;
    return `chat_${btoa(message.substring(0, 50))}_${userCharacteristics}`;
  }

  async saveContext(userId: string, sessionId: string): Promise<void> {
    const contextKey = `${userId}:${sessionId}`;
    const context = this.activeContexts.get(contextKey);
    
    if (context) {
      await cacheService.saveContext(contextKey, context, 86400); // 24 hours
    }
  }
}

export const aiChatService = new AIChatService();