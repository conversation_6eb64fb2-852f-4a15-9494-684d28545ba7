"use client";

import { useEffect, useRef, useState, use<PERSON><PERSON>back, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Brain,
  Zap,
  Target,
  BookOpen,
  TrendingUp,
  Network,
  Eye,
  EyeOff,
  RotateCcw,
  Maximize2,
  ZoomIn,
  ZoomOut,
  Search,
  Move,
  MousePointer,
  Filter
} from 'lucide-react';

interface ConceptNode {
  id: string;
  label: string;
  mastery: number;
  difficulty: number;
  category: string;
  isUnlocked: boolean;
  size: number;
  color: string;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
}

interface ConceptEdge {
  from: string;
  to: string;
  type: 'prerequisite' | 'related';
  strength: number;
}

interface ConceptMapProps {
  userId: string;
  onNodeClick?: (nodeId: string) => void;
  onNodeHover?: (nodeId: string | null) => void;
}

export default function ConceptMap({ userId, onNodeClick, onNodeHover }: ConceptMapProps) {
  const router = useRouter();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [nodes, setNodes] = useState<ConceptNode[]>([]);
  const [edges, setEdges] = useState<ConceptEdge[]>([]);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [showPrerequisites, setShowPrerequisites] = useState(true);
  const [showRelated, setShowRelated] = useState(true);
  const [isSimulating, setIsSimulating] = useState(true);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });

  // New interactive features
  const [isDragging, setIsDragging] = useState(false);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCategories, setFilteredCategories] = useState<string[]>([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [lastClickTime, setLastClickTime] = useState(0);

  useEffect(() => {
    loadConceptMap();
    
    const handleResize = () => {
      const container = canvasRef.current?.parentElement;
      if (container) {
        setDimensions({
          width: container.clientWidth,
          height: Math.max(400, container.clientHeight)
        });
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [userId]);

  useEffect(() => {
    if (isSimulating) {
      startSimulation();
    } else {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isSimulating]);

  const loadConceptMap = async () => {
    // Simulate loading concept map data
    // In production, this would call memorySystem.generateDynamicConceptMap(userId)
    const mockNodes: ConceptNode[] = [
      {
        id: 'diversification',
        label: 'Diversification',
        mastery: 0.9,
        difficulty: 2,
        category: 'investing',
        isUnlocked: true,
        size: 25,
        color: '#3B82F6',
        x: dimensions.width * 0.3,
        y: dimensions.height * 0.4
      },
      {
        id: 'asset_allocation',
        label: 'Asset Allocation',
        mastery: 0.7,
        difficulty: 3,
        category: 'investing',
        isUnlocked: true,
        size: 22,
        color: '#3B82F6',
        x: dimensions.width * 0.5,
        y: dimensions.height * 0.3
      },
      {
        id: 'sharpe_ratio',
        label: 'Sharpe Ratio',
        mastery: 0.4,
        difficulty: 6,
        category: 'analysis',
        isUnlocked: true,
        size: 18,
        color: '#F59E0B',
        x: dimensions.width * 0.7,
        y: dimensions.height * 0.5
      },
      {
        id: 'var',
        label: 'Value at Risk',
        mastery: 0.1,
        difficulty: 8,
        category: 'risk',
        isUnlocked: false,
        size: 15,
        color: '#6B7280',
        x: dimensions.width * 0.8,
        y: dimensions.height * 0.7
      },
      {
        id: 'convexity',
        label: 'Bond Convexity',
        mastery: 0.0,
        difficulty: 9,
        category: 'analysis',
        isUnlocked: false,
        size: 12,
        color: '#6B7280',
        x: dimensions.width * 0.6,
        y: dimensions.height * 0.8
      },
      {
        id: 'portfolio_theory',
        label: 'Portfolio Theory',
        mastery: 0.6,
        difficulty: 5,
        category: 'investing',
        isUnlocked: true,
        size: 20,
        color: '#3B82F6',
        x: dimensions.width * 0.4,
        y: dimensions.height * 0.6
      }
    ];

    const mockEdges: ConceptEdge[] = [
      { from: 'diversification', to: 'asset_allocation', type: 'prerequisite', strength: 0.8 },
      { from: 'asset_allocation', to: 'portfolio_theory', type: 'prerequisite', strength: 0.9 },
      { from: 'portfolio_theory', to: 'sharpe_ratio', type: 'prerequisite', strength: 0.7 },
      { from: 'sharpe_ratio', to: 'var', type: 'related', strength: 0.6 },
      { from: 'portfolio_theory', to: 'convexity', type: 'related', strength: 0.4 },
      { from: 'diversification', to: 'portfolio_theory', type: 'related', strength: 0.5 }
    ];

    setNodes(mockNodes);
    setEdges(mockEdges);
  };

  const drawCanvasRef = useRef<() => void>();
  const edgesRef = useRef(edges);
  const isSimulatingRef = useRef(isSimulating);

  // Update refs when values change
  useEffect(() => {
    edgesRef.current = edges;
  }, [edges]);

  useEffect(() => {
    isSimulatingRef.current = isSimulating;
  }, [isSimulating]);

  const startSimulation = useCallback(() => {
    const simulate = () => {
      setNodes(prevNodes => {
        const newNodes = [...prevNodes];

        // Apply forces
        newNodes.forEach(node => {
          if (!node.vx) node.vx = 0;
          if (!node.vy) node.vy = 0;

          // Center force
          const centerX = dimensions.width / 2;
          const centerY = dimensions.height / 2;
          const centerForce = 0.01;
          node.vx += (centerX - (node.x || 0)) * centerForce;
          node.vy += (centerY - (node.y || 0)) * centerForce;

          // Repulsion from other nodes
          newNodes.forEach(other => {
            if (other.id !== node.id) {
              const dx = (node.x || 0) - (other.x || 0);
              const dy = (node.y || 0) - (other.y || 0);
              const distance = Math.sqrt(dx * dx + dy * dy);

              if (distance > 0 && distance < 100) {
                const force = 500 / (distance * distance);
                node.vx += (dx / distance) * force;
                node.vy += (dy / distance) * force;
              }
            }
          });
        });

        // Apply edge forces using ref to avoid dependency
        edgesRef.current.forEach(edge => {
          const sourceNode = newNodes.find(n => n.id === edge.from);
          const targetNode = newNodes.find(n => n.id === edge.to);

          if (sourceNode && targetNode) {
            const dx = (targetNode.x || 0) - (sourceNode.x || 0);
            const dy = (targetNode.y || 0) - (sourceNode.y || 0);
            const distance = Math.sqrt(dx * dx + dy * dy);

            const idealDistance = edge.type === 'prerequisite' ? 80 : 120;
            const force = (distance - idealDistance) * 0.1 * edge.strength;

            if (distance > 0) {
              const fx = (dx / distance) * force;
              const fy = (dy / distance) * force;

              sourceNode.vx += fx;
              sourceNode.vy += fy;
              targetNode.vx -= fx;
              targetNode.vy -= fy;
            }
          }
        });

        // Update positions and apply damping
        newNodes.forEach(node => {
          node.vx *= 0.9; // Damping
          node.vy *= 0.9;

          node.x = Math.max(node.size, Math.min(dimensions.width - node.size, (node.x || 0) + node.vx));
          node.y = Math.max(node.size, Math.min(dimensions.height - node.size, (node.y || 0) + node.vy));
        });

        return newNodes;
      });

      // Call draw function if available
      if (drawCanvasRef.current) {
        drawCanvasRef.current();
      }

      // Continue animation using ref to avoid dependency
      if (isSimulatingRef.current) {
        animationRef.current = requestAnimationFrame(simulate);
      }
    };

    simulate();
  }, [dimensions.width, dimensions.height]);

  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, dimensions.width, dimensions.height);

    // Apply zoom and pan transformations
    ctx.save();
    ctx.translate(pan.x, pan.y);
    ctx.scale(zoom, zoom);
    
    // Draw edges
    edges.forEach(edge => {
      if (!showPrerequisites && edge.type === 'prerequisite') return;
      if (!showRelated && edge.type === 'related') return;

      const sourceNode = filteredNodes.find(n => n.id === edge.from);
      const targetNode = filteredNodes.find(n => n.id === edge.to);
      
      if (sourceNode && targetNode && sourceNode.x && sourceNode.y && targetNode.x && targetNode.y) {
        ctx.beginPath();
        ctx.moveTo(sourceNode.x, sourceNode.y);
        ctx.lineTo(targetNode.x, targetNode.y);
        
        ctx.strokeStyle = edge.type === 'prerequisite' ? '#3B82F6' : '#6B7280';
        ctx.lineWidth = edge.strength * 3;
        ctx.globalAlpha = 0.6;
        ctx.stroke();
        ctx.globalAlpha = 1;
        
        // Draw arrow for prerequisites
        if (edge.type === 'prerequisite') {
          const angle = Math.atan2(targetNode.y - sourceNode.y, targetNode.x - sourceNode.x);
          const arrowLength = 10;
          const arrowX = targetNode.x - Math.cos(angle) * (targetNode.size + 5);
          const arrowY = targetNode.y - Math.sin(angle) * (targetNode.size + 5);
          
          ctx.beginPath();
          ctx.moveTo(arrowX, arrowY);
          ctx.lineTo(
            arrowX - arrowLength * Math.cos(angle - Math.PI / 6),
            arrowY - arrowLength * Math.sin(angle - Math.PI / 6)
          );
          ctx.moveTo(arrowX, arrowY);
          ctx.lineTo(
            arrowX - arrowLength * Math.cos(angle + Math.PI / 6),
            arrowY - arrowLength * Math.sin(angle + Math.PI / 6)
          );
          ctx.stroke();
        }
      }
    });
    
    // Draw nodes
    filteredNodes.forEach(node => {
      if (!node.x || !node.y) return;

      const isSelected = selectedNode === node.id;
      const isHovered = hoveredNode === node.id;
      const isHighlighted = searchTerm && node.label.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Node circle
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.size, 0, 2 * Math.PI);
      
      // Color based on mastery and unlock status
      if (!node.isUnlocked) {
        ctx.fillStyle = '#374151';
      } else {
        const alpha = 0.3 + node.mastery * 0.7;
        ctx.fillStyle = `${node.color}${Math.round(alpha * 255).toString(16).padStart(2, '0')}`;
      }
      
      ctx.fill();
      
      // Border
      ctx.strokeStyle = isSelected ? '#F59E0B' : isHovered ? '#10B981' : isHighlighted ? '#EF4444' : node.color;
      ctx.lineWidth = isSelected ? 3 : isHovered ? 2 : isHighlighted ? 2 : 1;
      ctx.stroke();

      // Highlight ring for search results
      if (isHighlighted) {
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.size + 5, 0, 2 * Math.PI);
        ctx.strokeStyle = '#EF4444';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.stroke();
        ctx.setLineDash([]);
      }
      
      // Mastery ring
      if (node.isUnlocked && node.mastery > 0) {
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.size + 3, 0, 2 * Math.PI * node.mastery);
        ctx.strokeStyle = '#10B981';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
      
      // Lock icon for locked nodes
      if (!node.isUnlocked) {
        ctx.fillStyle = '#9CA3AF';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🔒', node.x, node.y + 4);
      }
      
      // Label
      ctx.fillStyle = '#FFFFFF';
      ctx.font = `${isSelected || isHovered ? 'bold ' : ''}12px Arial`;
      ctx.textAlign = 'center';
      ctx.fillText(node.label, node.x, node.y + node.size + 15);
      
      // Difficulty indicator
      ctx.fillStyle = '#6B7280';
      ctx.font = '10px Arial';
      ctx.fillText(`Lv.${node.difficulty}`, node.x, node.y + node.size + 28);
    });

    // Restore canvas state
    ctx.restore();
  }, [dimensions.width, dimensions.height, pan.x, pan.y, zoom, nodes, edges, showPrerequisites, showRelated, selectedNode, hoveredNode, searchTerm, filteredNodes]);

  // Update the ref when drawCanvas changes
  useEffect(() => {
    drawCanvasRef.current = drawCanvas;
  }, [drawCanvas]);

  // Redraw when static properties change (not during animation)
  useEffect(() => {
    if (!isSimulating) {
      drawCanvas();
    }
  }, [drawCanvas, isSimulating]);

  const getMousePosition = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: (event.clientX - rect.left - pan.x) / zoom,
      y: (event.clientY - rect.top - pan.y) / zoom
    };
  };

  const handleCanvasMouseDown = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const mousePos = getMousePosition(event);
    setLastMousePos(mousePos);

    // Find clicked node
    const clickedNode = nodes.find(node => {
      if (!node.x || !node.y) return false;
      const distance = Math.sqrt((mousePos.x - node.x) ** 2 + (mousePos.y - node.y) ** 2);
      return distance <= node.size;
    });

    if (clickedNode) {
      const currentTime = Date.now();
      const isDoubleClick = currentTime - lastClickTime < 300;
      setLastClickTime(currentTime);

      if (isDoubleClick) {
        // Double-click action - navigate to learning content
        handleNodeDoubleClick(clickedNode.id);
      } else {
        setSelectedNode(clickedNode.id);
        setDraggedNode(clickedNode.id);
        setIsDragging(true);
        onNodeClick?.(clickedNode.id);
      }
    } else {
      setSelectedNode(null);
      setDraggedNode(null);
    }
  };

  const handleNodeDoubleClick = (nodeId: string) => {
    // Navigate to learning content based on node
    switch (nodeId) {
      case 'diversification':
      case 'asset_allocation':
      case 'portfolio_theory':
        router.push('/learning?topic=' + nodeId);
        break;
      case 'sharpe_ratio':
      case 'var':
      case 'convexity':
        router.push('/chat?query=' + encodeURIComponent(`Explain ${nodeId} in detail with examples`));
        break;
      default:
        router.push('/learning');
    }
  };

  const handleCanvasMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const mousePos = getMousePosition(event);

    // Handle node dragging
    if (isDragging && draggedNode) {
      setNodes(prevNodes =>
        prevNodes.map(node =>
          node.id === draggedNode
            ? { ...node, x: mousePos.x, y: mousePos.y, vx: 0, vy: 0 }
            : node
        )
      );
      return;
    }

    // Handle panning
    if (event.buttons === 1 && !draggedNode) {
      const deltaX = (mousePos.x - lastMousePos.x) * zoom;
      const deltaY = (mousePos.y - lastMousePos.y) * zoom;
      setPan(prev => ({ x: prev.x + deltaX, y: prev.y + deltaY }));
    }

    // Find hovered node
    const hoveredNode = nodes.find(node => {
      if (!node.x || !node.y) return false;
      const distance = Math.sqrt((mousePos.x - node.x) ** 2 + (mousePos.y - node.y) ** 2);
      return distance <= node.size;
    });

    const newHoveredId = hoveredNode?.id || null;
    if (newHoveredId !== this.hoveredNode) {
      setHoveredNode(newHoveredId);
      onNodeHover?.(newHoveredId);
    }

    // Update cursor
    const canvas = canvasRef.current;
    if (canvas) {
      canvas.style.cursor = hoveredNode ? 'pointer' : isDragging ? 'grabbing' : 'grab';
    }

    setLastMousePos(mousePos);
  };

  const handleCanvasMouseUp = () => {
    setIsDragging(false);
    setDraggedNode(null);
  };

  const handleWheel = (event: React.WheelEvent<HTMLCanvasElement>) => {
    event.preventDefault();
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(3, zoom * zoomFactor));
    setZoom(newZoom);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(3, prev * 1.2));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(0.1, prev / 1.2));
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const filteredNodes = useMemo(() => {
    return nodes.filter(node => {
      const matchesSearch = !searchTerm ||
        node.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        node.category.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = filteredCategories.length === 0 ||
        filteredCategories.includes(node.category);

      return matchesSearch && matchesCategory;
    });
  }, [nodes, searchTerm, filteredCategories]);

  const toggleCategory = (category: string) => {
    setFilteredCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const resetZoom = () => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  };

  const centerOnNode = (nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node && node.x && node.y) {
      setPan({
        x: dimensions.width / 2 - node.x * zoom,
        y: dimensions.height / 2 - node.y * zoom
      });
    }
  };

  const resetLayout = () => {
    setNodes(prevNodes => 
      prevNodes.map(node => ({
        ...node,
        x: Math.random() * (dimensions.width - 100) + 50,
        y: Math.random() * (dimensions.height - 100) + 50,
        vx: 0,
        vy: 0
      }))
    );
  };

  const selectedNodeData = selectedNode ? nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className="space-y-6">
      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
        <CardHeader>
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Network className="h-5 w-5 text-purple-400" />
                <CardTitle className="text-white">Dynamic Concept Map</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="border-slate-600 text-slate-300"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex flex-wrap items-center gap-3">
              <div className="flex items-center gap-2 flex-1 min-w-[200px]">
                <Search className="h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search concepts..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomOut}
                  className="border-slate-600 text-slate-300"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="text-xs text-slate-400 min-w-[3rem] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomIn}
                  className="border-slate-600 text-slate-300"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetZoom}
                  className="border-slate-600 text-slate-300"
                  title="Reset Zoom & Pan"
                >
                  <MousePointer className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* View Controls */}
            <div className="flex flex-wrap items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPrerequisites(!showPrerequisites)}
                className={`border-slate-600 ${showPrerequisites ? 'bg-blue-500/20 text-blue-300' : 'text-slate-300'}`}
              >
                {showPrerequisites ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                Prerequisites
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowRelated(!showRelated)}
                className={`border-slate-600 ${showRelated ? 'bg-gray-500/20 text-gray-300' : 'text-slate-300'}`}
              >
                {showRelated ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                Related
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsSimulating(!isSimulating)}
                className="border-slate-600 text-slate-300"
              >
                {isSimulating ? 'Pause' : 'Play'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={resetLayout}
                className="border-slate-600 text-slate-300"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>

              {/* Category Filters */}
              {['investing', 'analysis', 'risk'].map(category => (
                <Button
                  key={category}
                  variant="outline"
                  size="sm"
                  onClick={() => toggleCategory(category)}
                  className={`border-slate-600 capitalize ${
                    filteredCategories.includes(category)
                      ? 'bg-purple-500/20 text-purple-300'
                      : 'text-slate-300'
                  }`}
                >
                  <Filter className="h-3 w-3 mr-1" />
                  {category}
                </Button>
              ))}
            </div>
          </div>
          <CardDescription className="text-slate-400">
            Interactive visualization of your financial knowledge network. Drag nodes, zoom, and search to explore concepts.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <canvas
              ref={canvasRef}
              width={dimensions.width}
              height={dimensions.height}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
              onWheel={handleWheel}
              className="border border-slate-600 rounded-lg bg-slate-900/50 cursor-grab"
            />
            
            {/* Legend */}
            <div className="absolute top-4 left-4 bg-slate-800/80 backdrop-blur-sm rounded-lg p-3 space-y-2">
              <div className="text-xs font-medium text-slate-300">Legend</div>
              <div className="flex items-center gap-2 text-xs text-slate-400">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                <span>Unlocked</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-slate-400">
                <div className="w-3 h-3 rounded-full bg-gray-500"></div>
                <span>Locked</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-slate-400">
                <div className="w-3 h-0.5 bg-blue-500"></div>
                <span>Prerequisite</span>
              </div>
              <div className="flex items-center gap-2 text-xs text-slate-400">
                <div className="w-3 h-0.5 bg-gray-500"></div>
                <span>Related</span>
              </div>
            </div>

            {/* Interactive Help */}
            <div className="absolute top-4 right-4 bg-slate-800/80 backdrop-blur-sm rounded-lg p-3 space-y-1">
              <div className="text-xs font-medium text-slate-300">Controls</div>
              <div className="text-xs text-slate-400">• Drag nodes to move</div>
              <div className="text-xs text-slate-400">• Double-click to learn</div>
              <div className="text-xs text-slate-400">• Scroll to zoom</div>
              <div className="text-xs text-slate-400">• Drag canvas to pan</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Node Details */}
      {selectedNodeData && (
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-400" />
                {selectedNodeData.label}
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-blue-500/10 text-blue-300 border-blue-500/30">
                  Level {selectedNodeData.difficulty}
                </Badge>
                <Badge variant="outline" className={`${
                  selectedNodeData.isUnlocked 
                    ? 'bg-green-500/10 text-green-300 border-green-500/30' 
                    : 'bg-gray-500/10 text-gray-300 border-gray-500/30'
                }`}>
                  {selectedNodeData.isUnlocked ? 'Unlocked' : 'Locked'}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-300">Mastery Progress</span>
                <span className="text-sm text-white">{Math.round(selectedNodeData.mastery * 100)}%</span>
              </div>
              <Progress value={selectedNodeData.mastery * 100} className="h-2" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-slate-300">Prerequisites</h4>
                <div className="space-y-1">
                  {edges
                    .filter(edge => edge.to === selectedNodeData.id && edge.type === 'prerequisite')
                    .map(edge => {
                      const prereqNode = nodes.find(n => n.id === edge.from);
                      return prereqNode ? (
                        <div key={edge.from} className="text-xs text-slate-400 flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            prereqNode.mastery > 0.6 ? 'bg-green-500' : 'bg-yellow-500'
                          }`}></div>
                          {prereqNode.label}
                        </div>
                      ) : null;
                    })}
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-slate-300">Related Concepts</h4>
                <div className="space-y-1">
                  {edges
                    .filter(edge => 
                      (edge.from === selectedNodeData.id || edge.to === selectedNodeData.id) && 
                      edge.type === 'related'
                    )
                    .map(edge => {
                      const relatedId = edge.from === selectedNodeData.id ? edge.to : edge.from;
                      const relatedNode = nodes.find(n => n.id === relatedId);
                      return relatedNode ? (
                        <div key={relatedId} className="text-xs text-slate-400 flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            relatedNode.isUnlocked ? 'bg-blue-500' : 'bg-gray-500'
                          }`}></div>
                          {relatedNode.label}
                        </div>
                      ) : null;
                    })}
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-slate-300">Actions</h4>
                <div className="space-y-2">
                  {selectedNodeData.isUnlocked ? (
                    <>
                      <Button
                        size="sm"
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={() => router.push('/learning?topic=' + selectedNodeData.id)}
                      >
                        <BookOpen className="h-3 w-3 mr-1" />
                        Study Concept
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-full border-slate-600"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent(`Ask me questions about ${selectedNodeData.label}`))}
                      >
                        <Brain className="h-3 w-3 mr-1" />
                        Ask AI
                      </Button>
                      {selectedNodeData.mastery > 0.5 && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="w-full border-slate-600"
                          onClick={() => router.push('/dashboard?focus=' + selectedNodeData.id)}
                        >
                          <Target className="h-3 w-3 mr-1" />
                          Practice
                        </Button>
                      )}
                    </>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full border-slate-600"
                      onClick={() => {
                        const prereqs = edges
                          .filter(edge => edge.to === selectedNodeData.id && edge.type === 'prerequisite')
                          .map(edge => nodes.find(n => n.id === edge.from)?.label)
                          .filter(Boolean)
                          .join(', ');
                        router.push('/learning?prerequisites=' + encodeURIComponent(prereqs));
                      }}
                    >
                      <BookOpen className="h-3 w-3 mr-1" />
                      Learn Prerequisites
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}