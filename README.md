# AI-Powered Personal Finance Strategist

A truly intelligent financial coach with reinforcement learning-inspired memory system, dynamic concept mapping, and modular microservices architecture. This platform evolves with each user interaction, creating a living, breathing educational ecosystem.

## 🚀 Features

### Core Functionality
- **Intelligent Memory System**: Reinforcement learning-inspired user vector tracking with behavioral pattern recognition
- **Dynamic Concept Map**: Interactive knowledge network that expands as users learn, showing connections between financial concepts
- **Modular Architecture**: Loosely coupled services for AI chat, quote engine, risk analysis, and user analytics
- **Advanced Caching**: Redis-inspired caching system with intelligent TTL and compression
- **Contextual AI Responses**: Gemini-powered assistant that adapts to user learning style and progress
- **Real-time Analytics**: Behavioral intelligence with engagement prediction and churn risk assessment

### Technical Features
- **Microservices Architecture**: Separate services for memory, chat, quotes, risk analysis, and analytics
- **Intelligent Caching**: Multi-layer caching with compression and smart eviction policies
- **Real-time Learning**: User behavior tracking with immediate adaptation
- **Supabase Integration**: Full database schema with RLS policies and performance indexes
- **Canvas-based Visualization**: Interactive concept map with force-directed layout simulation

## 🛠 Technology Stack

- **Framework**: Next.js 13 with App Router and TypeScript
- **AI Engine**: Google Gemini API with contextual memory integration
- **Database**: Supabase with comprehensive schema and RLS policies
- **Caching**: Intelligent multi-layer caching system with Redis-like capabilities
- **Visualization**: Canvas-based interactive concept mapping
- **Analytics**: Real-time user behavior tracking and prediction
- **Architecture**: Modular microservices with loose coupling

## 📊 Key Components

### 1. Memory System (`lib/services/memory-system.ts`)
- Reinforcement learning-inspired user vector tracking
- Concept mastery progression with prerequisite mapping
- Behavioral pattern recognition and prediction
- Dynamic learning path generation

### 2. AI Chat Service (`lib/services/ai-chat-service.ts`)
- Context-aware conversation management
- Real-time engagement monitoring
- Personalized response generation
- Smart suggestion algorithms

### 3. Dynamic Concept Map (`components/ConceptMap.tsx`)
- Interactive knowledge network visualization
- Force-directed layout simulation
- Real-time mastery progress tracking
- Prerequisite and relationship mapping

### 4. Risk Analysis Service (`lib/services/risk-analysis-service.ts`)
- Advanced portfolio risk metrics (VaR, Sharpe ratio, etc.)
- Scenario analysis and stress testing
- Personalized risk recommendations
- Real-time risk monitoring

### 5. User Analytics Service (`lib/services/user-analytics-service.ts`)
- Behavioral pattern analysis
- Engagement prediction and intervention
- Learning velocity tracking
- Churn risk assessment

### 6. Intelligent Caching (`lib/services/cache-service.ts`)
- Multi-layer caching with compression
- Smart eviction policies
- Performance analytics
- Redis-compatible interface

## 🔧 Setup Instructions

### Prerequisites
- Node.js 18+ 
- npm or yarn package manager
- Gemini API key (for AI functionality)

### Installation

1. **Clone and Install**
   ```bash
   npm install
   ```

2. **Environment Setup**
   Create a `.env.local` file:
   ```env
   NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Development Server**
   ```bash
   npm run dev
   ```

4. **Production Build**
   ```bash
   npm run build
   npm start
   ```

### Supabase Database Setup

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com) and create a new project
   - Copy your project URL and anon key to the environment variables

2. **Run Database Migration**
   - Copy the SQL from `supabase/migrations/create_intelligent_finance_schema.sql`
   - Run it in your Supabase SQL editor
   - This creates all tables, indexes, and security policies

3. **Verify Setup**
   - Check that all tables are created with proper RLS policies
   - Test the connection from your application

## 🎯 Usage Guide

### Getting Started
1. **Knowledge Map**: Explore the interactive concept network to see your learning progress
2. **AI Chat**: Engage with the intelligent assistant that adapts to your learning style
3. **Dashboard**: Monitor your financial health score and behavioral analytics
4. **Portfolio Simulation**: Test investment strategies with advanced risk analysis
5. **Learning Modules**: Follow personalized learning paths based on your progress

### Intelligent Features
- **Adaptive Learning**: The system learns your preferences and adjusts content complexity
- **Behavioral Insights**: Real-time analysis of your learning patterns and engagement
- **Personalized Recommendations**: AI-generated suggestions based on your progress
- **Dynamic Content**: Concept map and learning paths that evolve with your knowledge
- **Predictive Analytics**: Engagement prediction and proactive intervention

## 🏗 Architecture Overview

### Modular Services
- **Memory System**: Central intelligence for user learning and behavior tracking
- **AI Chat Service**: Context-aware conversation management with engagement monitoring
- **Quote Engine**: Dynamic market insights and personalized financial quotes
- **Risk Analysis**: Advanced portfolio risk assessment and scenario modeling
- **User Analytics**: Behavioral intelligence and predictive analytics
- **Cache Service**: High-performance caching with intelligent eviction

### Data Flow
1. User interactions are captured by the analytics service
2. Memory system processes and stores learning vectors
3. AI chat service uses context for personalized responses
4. Concept map updates in real-time based on progress
5. Cache service optimizes performance across all modules

### Caching Strategy
- **L1 Cache**: In-memory with LRU eviction
- **L2 Cache**: Browser localStorage for client-side persistence
- **L3 Cache**: Supabase for server-side persistence
- **Smart Compression**: Automatic compression for large data sets
- **TTL Management**: Intelligent expiration based on data type and usage patterns

## 🔐 Security Features

- **Row Level Security**: Comprehensive RLS policies in Supabase
- **Data Encryption**: All sensitive data encrypted at rest and in transit
- **Privacy by Design**: No PII storage, anonymous user vectors
- **Secure Caching**: Encrypted cache entries with automatic expiration
- **API Security**: Rate limiting and input validation

## 📱 Browser Compatibility

- **Canvas Visualization**: All modern browsers with HTML5 Canvas support
- **Real-time Features**: WebSocket support for live updates
- **Voice Integration**: Chrome, Edge, Safari (latest versions)
- **Mobile Optimized**: Touch-friendly concept map interactions
- **Progressive Enhancement**: Graceful degradation for older browsers

## 🚀 Deployment

### Production Deployment
- **Vercel**: Recommended for Next.js applications
- **Netlify**: Alternative with edge functions support
- **AWS**: Full cloud deployment with Lambda functions
- **Docker**: Containerized deployment for any platform

### Environment Configuration
```env
# Production environment variables
NEXT_PUBLIC_GEMINI_API_KEY=your_production_gemini_key
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_key
REDIS_URL=your_redis_url_for_caching (optional)
```

### Performance Optimization
- Enable Redis for production caching
- Configure CDN for static assets
- Set up database connection pooling
- Enable compression and minification

## 🤝 Contributing

This intelligent financial coach represents the cutting edge of AI-powered educational technology. The modular architecture makes it easy to extend and customize:

### Adding New Services
1. Create service in `lib/services/`
2. Implement standard interface patterns
3. Add caching layer integration
4. Update memory system for data tracking

### Extending the Concept Map
1. Add new concepts to the database
2. Define prerequisite relationships
3. Update visualization algorithms
4. Test learning path generation

## 📄 License

This project is open source and available under the MIT License.

---

**Note**: This intelligent financial coach showcases advanced AI and machine learning techniques applied to financial education. The system continuously learns and adapts, creating a truly personalized learning experience that evolves with each user interaction.